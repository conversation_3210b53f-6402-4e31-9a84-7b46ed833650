<?php
session_start();
include 'conn.php';

echo "<h2>KYC Investigation</h2>";

// Check if user is logged in
if (!isset($_SESSION['data']['username'])) {
    echo "<p style='color: red;'>❌ User not logged in</p>";
    echo "<p>Please login first to investigate KYC issue.</p>";
    exit();
}

$username = $_SESSION['data']['username'];
echo "<p><strong>Username:</strong> " . htmlspecialchars($username) . "</p>";

// 1. Check if user exists in database
echo "<h3>1. User Database Check:</h3>";
$userQuery = "SELECT * FROM users WHERE username = '$username'";
echo "<p><strong>Query:</strong> $userQuery</p>";

$userResult = mysqli_query($con, $userQuery);
if (!$userResult) {
    echo "<p style='color: red;'>❌ Query failed: " . mysqli_error($con) . "</p>";
    exit();
}

$userData = mysqli_fetch_assoc($userResult);
if (!$userData) {
    echo "<p style='color: red;'>❌ User not found in database</p>";
    exit();
}

echo "<p style='color: green;'>✅ User found in database</p>";
echo "<p><strong>User ID:</strong> " . $userData['id'] . "</p>";
echo "<p><strong>Email:</strong> " . $userData['email'] . "</p>";
echo "<p><strong>Name:</strong> " . $userData['name'] . "</p>";

// 2. Check KYC columns in database
echo "<h3>2. KYC Data in Database:</h3>";
echo "<p><strong>BVN:</strong> " . (empty($userData['bvn']) ? 'NULL/Empty' : htmlspecialchars($userData['bvn'])) . "</p>";
echo "<p><strong>NIN:</strong> " . (empty($userData['nin']) ? 'NULL/Empty' : htmlspecialchars($userData['nin'])) . "</p>";

// 3. Check if KYC columns exist
echo "<h3>3. Database Column Check:</h3>";
$columnsQuery = "DESCRIBE users";
$columnsResult = mysqli_query($con, $columnsQuery);
$columns = [];
while ($row = mysqli_fetch_assoc($columnsResult)) {
    $columns[] = $row['Field'];
}

if (in_array('bvn', $columns)) {
    echo "<p style='color: green;'>✅ 'bvn' column exists</p>";
} else {
    echo "<p style='color: red;'>❌ 'bvn' column missing</p>";
}

if (in_array('nin', $columns)) {
    echo "<p style='color: green;'>✅ 'nin' column exists</p>";
} else {
    echo "<p style='color: red;'>❌ 'nin' column missing</p>";
}

// 4. Test KYC update manually
echo "<h3>4. Manual KYC Update Test:</h3>";
if (isset($_POST['test_update'])) {
    $testBvn = $_POST['test_bvn'];
    $testNin = $_POST['test_nin'];
    
    $updateQuery = "UPDATE users SET bvn = '$testBvn', nin = '$testNin' WHERE username = '$username'";
    echo "<p><strong>Update Query:</strong> $updateQuery</p>";
    
    if (mysqli_query($con, $updateQuery)) {
        echo "<p style='color: green;'>✅ Manual update successful</p>";
        
        // Verify the update
        $verifyQuery = "SELECT bvn, nin FROM users WHERE username = '$username'";
        $verifyResult = mysqli_query($con, $verifyQuery);
        $verifyData = mysqli_fetch_assoc($verifyResult);
        
        echo "<p><strong>After Update:</strong></p>";
        echo "<p>BVN: " . (empty($verifyData['bvn']) ? 'NULL/Empty' : $verifyData['bvn']) . "</p>";
        echo "<p>NIN: " . (empty($verifyData['nin']) ? 'NULL/Empty' : $verifyData['nin']) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Manual update failed: " . mysqli_error($con) . "</p>";
    }
} else {
    echo "<form method='POST'>";
    echo "<p><strong>Test Manual Update:</strong></p>";
    echo "<p>BVN: <input type='text' name='test_bvn' placeholder='11 digits' maxlength='11'></p>";
    echo "<p>NIN: <input type='text' name='test_nin' placeholder='11 digits' maxlength='11'></p>";
    echo "<p><input type='submit' name='test_update' value='Test Update'></p>";
    echo "</form>";
}

// 5. Check recent KYC updates
echo "<h3>5. Recent Database Activity:</h3>";
$recentQuery = "SELECT * FROM users WHERE username = '$username' ORDER BY id DESC LIMIT 1";
$recentResult = mysqli_query($con, $recentQuery);
$recentData = mysqli_fetch_assoc($recentResult);

echo "<p><strong>Latest User Data:</strong></p>";
echo "<pre>" . print_r($recentData, true) . "</pre>";

// 6. Session vs Database comparison
echo "<h3>6. Session vs Database Comparison:</h3>";
echo "<p><strong>Session Username:</strong> " . htmlspecialchars($_SESSION['data']['username']) . "</p>";
echo "<p><strong>Database Username:</strong> " . htmlspecialchars($userData['username']) . "</p>";
echo "<p><strong>Match:</strong> " . ($_SESSION['data']['username'] === $userData['username'] ? '✅ Yes' : '❌ No') . "</p>";

echo "<hr>";
echo "<h3>Quick Actions:</h3>";
echo "<p><a href='web/app-/payvessel_kyc.php'>Complete KYC</a></p>";
echo "<p><a href='test_payvessel_api.php'>Test Payvessel API</a></p>";
echo "<p><a href='debug_kyc_status.php'>Check KYC Status</a></p>";

mysqli_close($con);
?> 