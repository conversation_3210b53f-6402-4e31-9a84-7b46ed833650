<?php
// error_reporting(0);
include "../../config.php";

if (!isset($_SESSION['username'])) {
  header('location:logout');
}
if ($_SESSION['token'] !== "1e8789816530b40d8784c371d829db38") {
  header('location:login.php');
}
if (!isset($_SESSION['LAST_ACTIVITY'])) {
  header('location:login.php');
}
if (time() - $_SESSION['LAST_ACTIVITY'] > 300000) {
  $last = $_SERVER['REQUEST_URI'];
  header("location:?last={$last}");
}
$_SESSION['LAST_ACTIVITY'] = time();
function parseAmt($amt)
{
  $val = intval($amt);
  if ($val > 999999) {
    return substr_replace(substr_replace($val, ',', -3, 0), ',', -7, 0);
  } elseif ($val > 999) {
    return substr_replace($val, ',', -3, 0);
  } else {
    return $val;
  }
}
if (($data['reserveBank'] == null || empty($data['reserveBank']) || $data['reserveBank'] == '[]') && !empty(json_decode($config['monnify'])[0])) {
  $username = $data['username'];
  $email = $data['email'];
  $name = $data['name'];
  try {
    $apik = trim(json_decode($config['monnify'])[0]);
    $seck = trim(json_decode($config['monnify'])[2]);
    $apiCon = trim(json_decode($config['monnify'])[1]);
    $au = base64_encode("{$apik}:{$seck}");

    // Login to get the access token
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://api.monnify.com/api/v1/auth/login");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_POST, TRUE);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Basic " . $au
    ));
    $response = curl_exec($ch);
    $curl_error = curl_error($ch); // Check for cURL errors
    curl_close($ch);

    // Check for cURL errors or empty response
    if ($response === false || empty($response)) {
        throw new Exception("Monnify Auth API call failed. cURL error: " . $curl_error);
    }

    $tk_data = json_decode($response);

    // Check if JSON decoding failed or responseBody/accessToken is missing
    if ($tk_data === null || !isset($tk_data->responseBody) || !isset($tk_data->responseBody->accessToken)) {
        // Log the actual response for debugging
        error_log("Monnify Auth: Invalid JSON or missing accessToken. Response: " . $response);
        throw new Exception("Monnify Auth failed: Invalid response structure.");
    }

    // Line 52: Now safe to access
    $tk = $tk_data->responseBody->accessToken;

    // Create the reserved account
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://api.monnify.com/api/v2/bank-transfer/reserved-accounts");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_POST, TRUE);
    $tRef = md5($name . $email . time());
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
      "accountName" => $name,
      "accountReference" => $tRef,
      "currencyCode" => "NGN",
      "contractCode" => $apiCon,
      "customerName" => $name,
      "customerEmail" => $email,
      "getAllAvailableBanks" => false,
      "preferredBanks" => ["50515", "232", "035", "070", "058"]
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer " . $tk
    ));
    $response = curl_exec($ch);
    $curl_error = curl_error($ch); // Check for cURL errors
    curl_close($ch);

    // Check for cURL errors or empty response
    if ($response === false || empty($response)) {
        throw new Exception("Monnify Reserve Account API call failed. cURL error: " . $curl_error);
    }

    // Handle and store the response
    $response_array = json_decode($response, true);

    // Check if JSON decoding failed or responseBody/accounts is missing/not an array
    if ($response_array === null || !isset($response_array['responseBody']) || !isset($response_array['responseBody']['accounts']) || !is_array($response_array['responseBody']['accounts'])) {
        // Log the actual response for debugging
        error_log("Monnify Reserve: Invalid JSON or missing accounts array. Response: " . $response);
        throw new Exception("Monnify Reserve Account failed: Invalid response structure.");
    }

    // Line 82: Now safe to access
    $accounts = $response_array['responseBody']['accounts'];

    $selectedData = [];
    // Line 84: Check if $accounts is not empty before looping (already checked if it's an array)
    if (!empty($accounts)) {
        foreach ($accounts as $account) {
            // Basic check for expected keys within each account
            if (isset($account['bankCode'], $account['bankName'], $account['accountNumber'], $account['accountName'])) {
                $selectedData[] = [
                  'bankCode' => $account['bankCode'],
                  'bankName' => $account['bankName'],
                  'accountNumber' => $account['accountNumber'],
                  'accountName' => $account['accountName']
                ];
            } else {
                 error_log("Monnify Reserve: Skipping account with missing keys. Account data: " . json_encode($account));
            }
        }
    }

    // Only update DB if we successfully extracted some account data
    if (!empty($selectedData)) {
        $genAccDet = json_encode($selectedData);
        $updateQuery = "UPDATE users SET reserveBank = ? WHERE username = ?";
        $stmt = mysqli_prepare($con, $updateQuery);
        mysqli_stmt_bind_param($stmt, "ss", $genAccDet, $username);
        if (!mysqli_stmt_execute($stmt)) {
             error_log("Failed to update reserveBank for user $username: " . mysqli_error($con));
             // Consider if this should throw an exception or just log
        }
         mysqli_stmt_close($stmt);
    } else {
        error_log("Monnify Reserve: No valid account data extracted for user $username.");
    }

  } catch (Throwable $e) {
    $current_date = date('Y-m-d H:i:s');
    $myfile = fopen("errorFile.txt", "a") or die("Unable to open file!");
    fwrite($myfile, '\r\n ' . $current_date . ': ' . $e . '\r\n');
    fclose($myfile);
  }
}

?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="">
  <meta name="author" content="">
  <meta name="generator" content="">
  <title><?= $config['site_name'] ?> – Mobile App</title>


  <!-- Favicons -->
  <link rel="apple-touch-icon" href="assets/img/sharesub-logo.png" sizes="180x180">
  <link rel="apple-touch-icon" href="assets/img/sharesub-logo.png" sizes="152x152">
  <link rel="apple-touch-icon" href="assets/img/sharesub-logo.png" sizes="167x167">
  <link rel="apple-touch-icon" href="assets/img/sharesub-logo.png" sizes="120x120">
  <link rel="icon" href="assets/img/favion32.png" sizes="32x32" type="image/png">
  <link rel="icon" href="assets/img/favion16.png" sizes="16x16" type="image/png">

  <!-- Google fonts-->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

  <!-- bootstrap icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">

  <!-- swiper carousel css -->
  <link rel="stylesheet" href="assets/vendor/swiperjs-6.6.2/swiper-bundle.min.css">

  <!-- nouislider CSS -->
  <link href="assets/vendor/nouislider/nouislider.min.css" rel="stylesheet">
  <link rel="stylesheet" href="lazy-dev/assets/css/bootstrap.min.css">
  <!-- date rage picker -->
  <link rel="stylesheet" href="assets/vendor/daterangepicker/daterangepicker.css">
  <script src="//ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.1.7/dist/sweetalert2.min.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.1.7/dist/sweetalert2.all.min.js"></script>




  <!-- style css for this template -->
  <link href="assets/css/style.css" rel="stylesheet" id="style">

  <!-- Custom App CSS -->
  <link href="assets/css/custom-app.css" rel="stylesheet">
  
  <!-- Custom color overrides -->
  <link href="assets/css/custom-colors.css" rel="stylesheet">
  <link href="assets/css/page-specific.css" rel="stylesheet">
  
  <!-- Dark Mode CSS -->
  <link href="assets/css/dark-mode.css" rel="stylesheet">
  
  <!-- Button Styles CSS -->
  <link href="assets/css/button-styles.css" rel="stylesheet">
  
  <!-- Custom page helper script -->
  <script src="assets/js/page-helper.js"></script>
  
  <!-- Custom greeting script -->
  <script src="assets/js/greetings.js"></script>
</head>

<body class="body-scroll" data-page="wallet" onload="greet(); alertinfo()">

  <!-- loader section -->
  <!--
  <div class="container-fluid loader-wrap">
    <div class="row h-100">
      <div class="col-10 col-md-6 col-lg-5 col-xl-3 mx-auto text-center align-self-center">
        <div class="logo-wallet">
          <div class="wallet-bottom">
          </div>
          <div class="wallet-cards"></div>
          <div class="wallet-top">
          </div>
        </div>
        <p class="mt-4"><span class="text-secondary">Loading <?= $config['site_name'] ?></span><br><strong>Please
            Wait...</strong></p>
      </div>
    </div>
  </div>
  -->
  <!-- loader section ends -->

  <!-- Sidebar main menu -->
  <div class="sidebar-wrap sidebar-overlay">
    <!-- Add pushcontent or fullmenu instead overlay -->
    <div class="closemenu text-muted">Close Menu</div>
    <div class="sidebar">
        <!-- user information -->
        <div class="row my-3">
            <div class="col-12 profile-sidebar">
                <div class="clearfix"></div>
                <!-- <div class="circle small one"></div> --> <!-- Optional: Keep or remove decorative circles -->
                <!-- <div class="circle small two"></div> -->
                <!-- Commenting out the background SVG -->
                <!--
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 194.287 141.794" class="menubg">
                    <defs>
                        <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
                            <stop offset="0" stop-color="#09b2fd" />
                            <stop offset="1" stop-color="#6b00e5" />
                        </linearGradient>
                    </defs>
                    <path id="menubg" d="M672.935,207.064c-19.639,1.079-25.462-3.121-41.258,10.881s-24.433,41.037-49.5,34.15-14.406-16.743-50.307-29.667-32.464-19.812-16.308-41.711S500.472,130.777,531.872,117s63.631,21.369,93.913,15.363,37.084-25.959,56.686-19.794,4.27,32.859,6.213,44.729,9.5,16.186,9.5,26.113S692.573,205.985,672.935,207.064Z" transform="translate(-503.892 -111.404)" fill="url(#linear-gradient)" />
                </svg>
                -->

                <div class="row mt-3">
                    <div class="col-auto">
                        <figure class="avatar avatar-80 rounded-20 p-1 bg-white shadow-sm">
                            <img src="assets/img/user1.jpg" alt="" class="rounded-18">
                        </figure>
                    </div>
                    <div class="col px-0 align-self-center">
                        <h5 class="mb-2"><?= explode(' ', $data['name'])[1] ?></h5>
                        <p class="text-muted size-12">Balance: ₦<?= parseAmt($data['bal']) ?></p>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="darkmodeswitch">
                            <label class="form-check-label text-muted px-2 " for="darkmodeswitch">Dark Mode</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- user menu navigation -->
        <div class="row">
            <div class="col-12">
                <!-- Use a simpler list structure -->
                <ul class="nav flex-column sidebar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="welcome.php">
                            <i class="bi bi-house-door me-2"></i> <!-- Icon -->
                            <span>Dashboard</span> <!-- Text -->
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="data.php" tabindex="-1">
                             <i class="bi bi-wifi me-2"></i> <!-- Changed Icon -->
                             <span>Buy Data</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="airtime.php" tabindex="-1">
                            <i class="bi bi-phone me-2"></i> <!-- Changed Icon -->
                            <span>Buy Airtime</span>
                        </a>
                    </li>

                    <!-- Replaced Dropdown with Direct Links -->
                     <li class="nav-item">
                        <a class="nav-link" href="card.php" tabindex="-1">
                            <i class="bi bi-credit-card me-2"></i>
                            <span>Fund Wallet (Card)</span>
                        </a>
                    </li>
                     <li class="nav-item">
                        <a class="nav-link" href="manual.php" tabindex="-1">
                            <i class="bi bi-cash-coin me-2"></i>
                            <span>Fund Wallet (Manual)</span>
                        </a>
                    </li>
                    <!-- End of Replaced Dropdown -->

                    <li class="nav-item">
                        <a class="nav-link" href="transactions.php" tabindex="-1">
                            <i class="bi bi-receipt me-2"></i> <!-- Changed Icon -->
                            <span>Transactions</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="profile.php" tabindex="-1">
                           <i class="bi bi-person-circle me-2"></i>
                           <span>My Profile</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <!-- Add SweetAlert confirmation directly if preferred -->
                        <a class="nav-link" href="logout.php" onclick="event.preventDefault(); Swal.fire({title:'Logout', text:'Are you sure you want to log out?', icon:'question', showCancelButton: true, confirmButtonText: 'Yes, Logout'}).then((result) => { if (result.isConfirmed) { window.location.href='logout.php'; } }); return false;" tabindex="-1">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            <span>Logout</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
  </div>
  <!-- Sidebar main menu ends -->

  <!-- Add CSS for Sidebar -->
  <style>
  .sidebar-nav .nav-link {
      display: flex;
      align-items: center;
      padding: 0.8rem 1rem; /* Adjust padding */
      color: #495057; /* Default text color */
      font-weight: 500;
      border-radius: 0.375rem; /* Match Bootstrap's rounded corners */
      margin-bottom: 0.25rem; /* Space between items */
  }

  .sidebar-nav .nav-link:hover {
      background-color: #e9ecef; /* Light background on hover */
      color: #0d6efd; /* Primary color on hover */
  }

  .sidebar-nav .nav-link.active {
      background-color: #0d6efd; /* Primary background for active */
      color: #fff; /* White text for active */
  }

  .sidebar-nav .nav-link i {
      font-size: 1.1rem; /* Adjust icon size */
      width: 24px; /* Fixed width for alignment */
      text-align: center;
  }
  </style>

  <!-- Begin page -->
  <main class="h-100">

    <!-- Header -->
    <header class="header position-fixed">
      <div class="row">
        <div class="col-auto">
          <a href="javascript:void(0)" target="_self" class="btn btn-light btn-44 menu-btn">
            <i class="bi bi-list"></i>
          </a>
        </div>

        <div class="col text-center">
          <div class="logo-small">

            <!-- <img src="assets/img/logo.png" alt="" /> -->
            <h5><?= $config['site_name'] ?></h5>
          </div>
        </div>
        <div class="col-auto">
          <a href="profile.php" target="_self" class="btn btn-light btn-44">
            <i class="bi bi-person-circle"></i>
            <span class="count-indicator"></span>
          </a>
        </div>
      </div>
    </header>
    <!-- Header ends -->