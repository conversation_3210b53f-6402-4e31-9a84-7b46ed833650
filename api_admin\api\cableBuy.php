<?php 
$actual_link = $_SERVER['REMOTE_ADDR'];
//if($actual_link != '198.187.31.83') die('Invalid REQUEST');


include "../../conn.php";

if ($_SERVER["REQUEST_METHOD"] == "GET") {
	die(json_encode(array('error' => true, 'desc' => 'Unsupported Method')));
}
$headers = apache_request_headers();
if (!isset($headers['Authorization']) || empty($headers['Authorization'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
}


if (!isset($_POST['card_number']) || empty($_POST['card_number'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid card_number')));
}

if (!isset($_POST['service_id']) || empty($_POST['service_id'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid service_id')));
}

if (!isset($_POST['decoder_name']) || empty($_POST['decoder_name'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid decoder_name')));
}

$card_number = mysqli_real_escape_string($con, $_POST['card_number']);
$service_id = mysqli_real_escape_string($con, $_POST['service_id']);
$decoder_name = mysqli_real_escape_string($con, $_POST['decoder_name']);

$req_token = mysqli_real_escape_string($con, $headers['Authorization']);

if (!str_contains($req_token, 'Token')) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
}

if (!isset(explode(' ', $req_token)[1]) || empty(explode(' ', $req_token)[1])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
}

$token = explode(' ', $req_token)[1];

$cable = [];
$nets = mysqli_query($con, "SELECT * FROM cables");
while ($net = mysqli_fetch_assoc($nets)) {
	if ($net['plan_id'] == $service_id) {
		$cable[0] = $net['plan_id'];
		$cable[1] = $net['name'];
		$cable[2] = $net['amount'];
	}
}
if ($cable == []) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid service_id')));
}

$plan_id = $cable[0];
$bill_conf = mysqli_fetch_array(mysqli_query($con, "SELECT * FROM bill_config"));
$usertype = $user['type'];
$type = 'CABLE SUBSCRIPTION';
$t_desc = "CABLE SUBSCRIPTION";
$status = 'pending';
$t_date = $current_date;
$ref = md5($t_date);
$channel = $cable[1];

$netsToken = mysqli_query($con, "SELECT * FROM apikey WHERE platform='vtpass' AND types='airtime' ");
$tokenKey = mysqli_fetch_assoc($netsToken);
if (!$tokenKey) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid apikey')));
}
$apiLink = $tokenKey['apiLink'];
$apiKey = $tokenKey['apiKey'];
$secretkey = $tokenKey['secretkey'];

if ($bill_conf['source'] == 'vtpass') {
	$mtype;
	if (strtolower($decoder_name) == 'gotv') {
		$mtype = 'gotv';
	}elseif (strtolower($decoder_name) == 'dstv') {
		$mtype = 'dstv';
	}elseif (strtolower($decoder_name) == 'startime' || strtolower($decoder_name) == 'startimes') {
		$mtype = 'startimes';
	}
	$responseD = fromVtpass($ref, $mtype, $plan_id, $card_number, $apiLink, $apiKey, $secretkey);
	$resp = json_decode($responseD);
	$platform = 'VTPASS';
	if (isset($resp->code) && $resp->code == '000') {

		$status = 'success';
		mysqli_query($con, "INSERT INTO api_transactions (type, t_desc, status, t_date, ref, channel, platform, detail) VALUES ('$type', '$t_desc', '$status', '$t_date', '$ref', '$channel', '$platform', '$responseD')");

		die(json_encode(array('error' => false, 'status' => 202, 'desc' => $t_desc.' Placed Successfully')));
	}else{
		$status = 'failed';
		mysqli_query($con, "INSERT INTO api_transactions (type, t_desc, status, t_date, ref, channel, platform, detail) VALUES ('$type', '$t_desc', '$status', '$t_date', '$ref', '$channel', '$platform', '$responseD')");
		die(json_encode(array('error' => true, 'status' => 400, 'desc' => $resp->response_description)));
	}
}else{
    die('404');
}

function fromVtpass($ref, $service_id, $plan_id,$card_number, $apiLink, $apiKey, $secretkey){
	$payload = ['api-key: ' . $apiKey, 'secret-key: ' . $secretkey];
	$curl = curl_init();
	curl_setopt_array($curl, array(
		CURLOPT_URL => $apiLink,
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => array('request_id' => $ref,'serviceID' => $service_id, 'phone' => '09090909090','variation_code' => $plan_id,'billersCode' => $card_number),
		CURLOPT_HTTPHEADER => $payload,
	));
	$response = curl_exec($curl);
	curl_close($curl);
	return $response;
}
