<?php
// Check database columns for Payvessel KYC system
include 'conn.php';

echo "<h2>Database Columns Check</h2>";

// Check users table structure
echo "<h3>Users Table Columns:</h3>";
try {
    $result = mysqli_query($con, "DESCRIBE users");
    $columns = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $columns[] = $row['Field'];
        echo "<p>✅ " . $row['Field'] . " - " . $row['Type'] . "</p>";
    }
    
    echo "<h3>Required Columns Check:</h3>";
    
    // Check KYC columns
    if (in_array('bvn', $columns)) {
        echo "<p style='color: green;'>✅ bvn column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ bvn column missing - NEEDS TO BE ADDED</p>";
    }
    
    if (in_array('nin', $columns)) {
        echo "<p style='color: green;'>✅ nin column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ nin column missing - NEEDS TO BE ADDED</p>";
    }
    
    // Check Payvessel columns
    if (in_array('payvessel_accounts', $columns)) {
        echo "<p style='color: green;'>✅ payvessel_accounts column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ payvessel_accounts column missing - NEEDS TO BE ADDED</p>";
    }
    
    // Check config table
    echo "<h3>Config Table Check:</h3>";
    $configResult = mysqli_query($con, "DESCRIBE config");
    $configColumns = [];
    while ($row = mysqli_fetch_assoc($configResult)) {
        $configColumns[] = $row['Field'];
    }
    
    if (in_array('payvessel', $configColumns)) {
        echo "<p style='color: green;'>✅ payvessel column exists in config table</p>";
    } else {
        echo "<p style='color: red;'>❌ payvessel column missing in config table - NEEDS TO BE ADDED</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Summary:</h3>";
echo "<p>If any columns are missing, we need to add them to the database.</p>";
?> 