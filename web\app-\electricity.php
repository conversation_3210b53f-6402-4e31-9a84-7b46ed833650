<?php include('lazy-includes/header2.php');

$bill_conf = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM bill_config"));
$uType = $data['type'];

?>









<!-- main page content -->
<div class="main-container container">


  <div class="row">
    <div class="col-md-12">
      <div class="card">


        <div class="card-header">
          <div class="card-title">
            BUY ELECTRICITY
          </div>
        </div>





        <div class="card-body">
          <div class="row">
            <div class="col-md-6 col-lg-4">



              <div class="row mb-1 justify-content-center">
                <div class="col-12 col-md-8 col-lg-6">


                  <!-- start -->






                  <div class="form-group">
                    <label>DISCO</label>
                    <select id="disco" class="form-control" name="disco" required>
                      <?php
                      $bQ = mysqli_query($con, "SELECT * FROM bills WHERE status = 'active' ORDER BY plan_id");
                      while ($b = mysqli_fetch_assoc($bQ)) {
                        echo "<option value='" . $b['plan_id'] . "'>" . $b['name'] . "</option>";
                      }
                      ?>

                    </select>
                  </div>
                  <div class="form-group">
                    <label>Meter Type</label>
                    <select id="mtype" class="form-control" name="meterType" required>
                      <option value="PREPAID">Prepaid</option>
                      <option value="POSTPAID">Postpaid</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label>Meter Number</label>
                    <input type="number" id="mnumber" name="meterNumber" class="form-control" placeholder="Meter Number" required />
                  </div>
                  <button class="w-100 p-3 btn btn-primary mr-2" id="val" onclick="validateBill()">Validate</button>
                  <!-- <div class="form-group">
              <label >Meter Number</label>
              <input type="number" id="mnumber" name="meterNumber" class="form-control" placeholder="Meter Number" required />
            </div> -->
                  <div id="prp" class="mt-4" style="display: none;">
                    <div class="form-group">
                      <label>Name on Meter</label>
                      <input type="text" class="form-control" id="mname" placeholder="Name on Meter" readonly />
                    </div>
                    <div class="form-group">
                      <label>Address</label>
                      <input type="text" class="form-control" id="addrs" placeholder="Address" readonly />
                    </div>
                  </div>

                  <div id="misc" style="display: none;">
                    <div class="form-group">
                      <label>Amount</label>
                      <input type="number" oninput="amtChg(this.value)" name="amount" id="amount" class="form-control">
                    </div>
                    <div class="form-group">
                      <label>Amount Paying</label>
                      <input type="text" name="amount_paying" id="amount_paying" class="form-control" disabled="">
                    </div>
                    <p>
                      <span><b><?= $bill_conf['method'] == 'percentage' ? 'Discount' : 'Charge' ?>:</b></span> <?= $bill_conf['method'] == 'percentage' ? '' : '₦' ?><?= $bill_conf['method'] == 'percentage' ? $bill_conf[$uType] : $bill_conf[$uType] ?><?= $bill_conf['method'] == 'percentage' ? '%' : '' ?>
                    </p>
                    <div class="form-group">
                      <label>Phone Number</label>
                      <input type="number" id="phone" class="form-control" placeholder="Phone Number" required />
                    </div>
                    <button type="submit" name="topup" onclick="buynow()" class="w-100 p-3 btn btn-primary mr-2"> Buy </button>
                    <br>




                    <div class="col-md-12" id="card">

                      <div class="table-responsive">
                        <h4>ELECTRICITY TRANSACTIONS</h4>
                        <table class="table">
                          <thead class=" text-primary">
                            <th>
                              #
                            </th>
                            <th>
                              Token
                            </th>
                            <th>
                              Disco
                            </th>
                            <th>
                              Meter Number
                            </th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Phone Number</th>
                            <th>Name</th>
                            <th>Address</th>
                            <th>Date</th>
                            <th>Status</th>
                          </thead>
                          <tbody>
                            <?php
                            $email = $data['email'];
                            $q = mysqli_query($con, "SELECT * FROM transactions WHERE email = '$email' AND type LIKE '%Bill%' ORDER BY id desc");
                            if (mysqli_num_rows($q) > 0) {
                              $n = 0;
                              while ($d = mysqli_fetch_assoc($q)) {
                                $det = json_decode($d['t_desc']);
                                $n++;
                            ?>
                                <tr>
                                  <td>
                                    <?= $n ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= $det->token ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->disco) ? $det->disco : '' ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->meter) ? $det->meter : '' ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->type) ? $det->type : '' ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->amount) ? $det->amount : '' ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->phone) ? $det->phone : '' ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->name) ? $det->name : '' ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->address) ? $det->address : '' ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->date) ? $det->date : '' ?>
                                  </td>
                                  <td style="word-wrap: break-word;">
                                    <?= isset($det->status) ? $det->status : '' ?>
                                  </td>
                                </tr>
                            <?php
                              }
                            } else {
                              echo "No Bill Transaction Yet";
                            }
                            ?>
                          </tbody>
                        </table>
                      </div>
                    </div>



                    <!-- end -->

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <script>
    const billDisT = "<?= $bill_conf['method'] ?>";
    const billState = "<?= $bill_conf['status'] ?>";
    const billDis = <?= $bill_conf[$uType] ?>;

    function isJsonString(str) {
      try {
        JSON.parse(str);
      } catch (e) {
        return false;
      }
      return true;
    }
    let validated = false;
    // let ext = "<?= $_SESSION['config']['bill_profit'] ?>";
    function amtChg(r_amount) {
      // document.getElementById('amount_paying').value = '₦';
      if (r_amount != '') {
        console.log(r_amount)
        var amt = parseInt(r_amount);
        if (billDisT == 'percentage') {
          document.getElementById('amount_paying').value = '₦' + (amt - (billDis / 100 * amt));
        } else if (billDisT == 'discount') {
          document.getElementById('amount_paying').value = '₦' + (billDis + amt);
        }
        // document.getElementById('amtt').innerHTML = (parseInt(amt) + parseInt(ext));
      }
    }
    // document.getElementById('ch').innerHTML = ext;
    function buynow() {
      SlickLoader.setText("Loading...", "Please Wait");
      SlickLoader.enable();
      let disc = document.getElementById('disco').value;
      let disco = 0;
      if (disc == 'ikeja-electric') {
        disco = 1
      } else if (disc == 'eko-electric') {
        disco = 2
      } else if (disc == 'abuja-electric') {
        disco = 3
      } else if (disc == 'port-harcourt-electric') {
        disco = 6
      } else if (disc == 'ibadan-electric') {
        disco = 7
      } else if (disc == 'jos-electric') {
        disco = 8
      } else if (disc == 'yola-electric') {
        disco = 10
      } else if (disc == 'enugu-electric') {
        disco = 11
      } else if (disc == 'kano-electric') {
        disco = 12
      } else if (disc == 'benin-electric') {
        disco = 13
      }
      let mname = document.getElementById('mname').value;
      let address = document.getElementById('addrs').value;
      let meter_num = document.getElementById('mnumber').value;
      let meter_type = document.getElementById('mtype').value;
      let amount = document.getElementById('amount').value;
      let phone = document.getElementById('phone').value;
      if (validated) {
        $.post('checkbill', {
          tobill: 'isset',
          meter_num: meter_num,
          meter_type: meter_type,
          mname: mname,
          addrs: address,
          amount: amount,
          phone: phone,
          disc: disc
        }, (rs) => {
          SlickLoader.disable();
          console.log(rs);
          if (rs == 400) {
            Swal.fire('Something is missing', 'Please Check All Fields', 'error')
          } else if (rs == 423) {
            Swal.fire('Invalid Amount', 'Minimum Amount is ₦100', 'error')
          } else if (rs == '004') {
            Swal.fire('Insufficient Wallet Balance', 'Please Fund Your Wallet And Try Again', 'error')
          } else if (rs == 332) {
            Swal.fire('Something Went Wrong', 'Please Try Again', 'error')
          } else {
            if (rs != '' && isJsonString(rs)) {
              let res = JSON.parse(rs);
              if (res[0] == 200) {
                Swal.fire('Successful', res[1], 'success').then(() => {
                  location.reload()
                })
              }
            } else {
              Swal.fire('Error', rs, 'error')
            }
          }
        })
      } else {
        SlickLoader.disable();
        Swal.fire('Meter Number Not Validated', '', 'error')
      }
    }

    function validateBill() {
      if (billState == 'disable') {
        return Swal.fire('UNAVAILABLE', 'ELECTRICITY PAYMENT NOT AVAILABLE AT THE MOMENT, PLEASE TRY AGAIN LATER', 'warning')
      }
      validated = false;
      let disco = document.getElementById('disco').value;
      let meter_num = document.getElementById('mnumber').value;
      let meter_type = document.getElementById('mtype').value;
      if (meter_num == '') {
        Swal.fire('Meter Number is required', '', 'error')
      } else if (meter_num.length < 7) {
        Swal.fire('Invalid Meter Number', 'Please Check And Try Again', 'error');
      } else {
        // document.getElementById('prp').style.display = 'none';
        $("#prp").fadeOut();
        $("#err").fadeOut();
        $("#misc").fadeOut();
        document.getElementById('mname').value = '';
        document.getElementById('addrs').value = '';
        document.getElementById('val').style.opacity = 0.6;
        document.getElementById('val').innerHTML = 'Validating Please Wait...';
        SlickLoader.setText("Validating...", "Please Wait");
        SlickLoader.enable();
        $.post('checkbill', {
          disco: disco,
          meter_num: meter_num,
          meter_type: meter_type,
          checkbill: 'check'
        }, (r) => {
          console.log(r);
          let res = JSON.parse(r);
          SlickLoader.disable();
          console.log(r)
          document.getElementById('val').style.opacity = 1;
          document.getElementById('val').innerHTML = 'Validate Again';
          validated = true;
          if (res[0]) {
            // document.getElementById('prp').style.display = 'block';
            $("#prp").fadeIn();
            document.getElementById('mname').value = res[1];
            document.getElementById('addrs').value = res[2];
            $("#misc").fadeIn();
          } else if (res[0] == false) {
            Swal.fire('Error', res[1], 'info');
          } else {
            Swal.fire('Unknown Error', 'Please Check Your Internet Connectivity', 'error')
          }
        })
      }
    }
  </script>

  <!-- main page content ends -->



  <?php 
  // Get primary WhatsApp number for widget
  $whatsapp_numbers = [];
  if (isset($config['whatsapp_num']) && !empty($config['whatsapp_num'])) {
      // Try decoding as JSON array first
      $decoded_whatsapp = json_decode($config['whatsapp_num'], true);
      if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_whatsapp)) {
          $whatsapp_numbers = $decoded_whatsapp;
      } elseif (is_string($config['whatsapp_num'])) {
           // Fallback: treat as a single number if not valid JSON
          $whatsapp_numbers = [$config['whatsapp_num']];
      }
  }

  // Get primary WhatsApp number for widget
  $primary_whatsapp = !empty($whatsapp_numbers) ? $whatsapp_numbers[0] : '';
  // Clean the number (remove non-digits)
  $primary_whatsapp_clean = preg_replace('/[^0-9]/', '', $primary_whatsapp);
  // Ensure it starts with country code, assuming 234 if it starts with 0 and is local length
  if (substr($primary_whatsapp_clean, 0, 1) === '0' && (strlen($primary_whatsapp_clean) == 11 || strlen($primary_whatsapp_clean) == 10)) {
      $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 1);
  } elseif (substr($primary_whatsapp_clean, 0, 3) === '234' && strlen($primary_whatsapp_clean) > 13) {
      // Handle cases like +2340... by removing the 0
      $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 3);
  } elseif (substr($primary_whatsapp_clean, 0, 1) === '+') {
      $primary_whatsapp_clean = substr($primary_whatsapp_clean, 1); // Remove leading + if present
  }
  ?>

  <?php if (!empty($primary_whatsapp_clean)): ?>
  <div class="whatsapp-widget">
      <!-- Floating chat button -->
      <button class="whatsapp-button" id="openWhatsappChat">
          <i class="bi bi-whatsapp"></i>
      </button>

      <!-- Chat popup -->
      <div class="whatsapp-popup" id="whatsappChatPopup">
          <div class="popup-header">
              <div class="popup-header-content">
                  <img src="../../sharesublogo.png" alt="<?= htmlspecialchars($config['site_name']) ?>" width="40">
                  <div>
                      <h6 class="mb-0"><?= htmlspecialchars($config['site_name']) ?> Support</h6>
                      <small class="text-muted">Usually replies within an hour</small>
                  </div>
              </div>
              <button class="close-btn" id="closeWhatsappChat">×</button>
          </div>
          <div class="popup-body">
              <div class="message-container">
                  <div class="received-message">
                      <p>Hello! How can we help you today?</p>
                      <small class="message-time">Now</small>
                  </div>
              </div>
              <div class="message-input">
                  <form id="whatsappMessageForm">
                      <input type="text" id="whatsappMessage" placeholder="Type a message..." required>
                      <button type="submit">
                          <i class="bi bi-send-fill"></i>
                      </button>
                  </form>
              </div>
          </div>
      </div>
  </div>

  <!-- WhatsApp Widget Styles -->
  <style>
      .whatsapp-widget {
          position: fixed;
          bottom: 20px;
          right: 20px;
          z-index: 1000;
      }

      .whatsapp-button {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background-color: #25D366;
          color: white;
          border: none;
          font-size: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
          transition: all 0.3s;
      }

      .whatsapp-button:hover {
          transform: scale(1.05);
          box-shadow: 0 6px 14px rgba(0, 0, 0, 0.2);
      }

      .whatsapp-popup {
          position: absolute;
          bottom: 80px;
          right: 0;
          width: 320px;
          background-color: white;
          border-radius: 10px;
          overflow: hidden;
          box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
          display: none;
      }

      .popup-header {
          background-color: #25D366;
          color: white;
          padding: 15px;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
      }

      .popup-header-content {
          display: flex;
          align-items: center;
          gap: 10px;
      }

      .popup-header img {
          border-radius: 50%;
          background-color: white;
          padding: 5px;
      }

      .close-btn {
          background: none;
          border: none;
          color: white;
          font-size: 24px;
          cursor: pointer;
      }

      .popup-body {
          display: flex;
          flex-direction: column;
          height: 300px;
      }

      .message-container {
          flex-grow: 1;
          padding: 15px;
          overflow-y: auto;
      }

      .received-message,
      .sent-message {
          max-width: 80%;
          margin-bottom: 10px;
          padding: 10px;
          border-radius: 10px;
      }

      .received-message {
          background-color: #f0f0f0;
          align-self: flex-start;
      }

      .sent-message {
          background-color: #dcf8c6;
          align-self: flex-end;
          margin-left: auto;
      }

      .message-time {
          display: block;
          font-size: 10px;
          margin-top: 5px;
          opacity: 0.6;
      }

      .message-input {
          border-top: 1px solid #e0e0e0;
          padding: 10px;
      }

      .message-input form {
          display: flex;
      }

      .message-input input {
          flex-grow: 1;
          border: none;
          padding: 10px;
          outline: none;
      }

      .message-input button {
          background-color: transparent;
          border: none;
          color: #25D366;
          cursor: pointer;
          font-size: 20px;
      }
  </style>

  <!-- WhatsApp Widget Script -->
  <script>
      document.addEventListener('DOMContentLoaded', function() {
          const openBtn = document.getElementById('openWhatsappChat');
          const closeBtn = document.getElementById('closeWhatsappChat');
          const popup = document.getElementById('whatsappChatPopup');
          const messageForm = document.getElementById('whatsappMessageForm');
          const messageInput = document.getElementById('whatsappMessage');
          const messageContainer = document.querySelector('.message-container');
          
          // WhatsApp number from database
          const whatsappNumber = '<?= $primary_whatsapp_clean ?>';
          
          // Toggle chat popup
          openBtn.addEventListener('click', function() {
              popup.style.display = 'block';
          });
          
          closeBtn.addEventListener('click', function() {
              popup.style.display = 'none';
          });
          
          // Handle message sending
          messageForm.addEventListener('submit', function(e) {
              e.preventDefault();
              
              const message = messageInput.value.trim();
              if (message) {
                  // Add sent message to chat
                  const sentMsg = document.createElement('div');
                  sentMsg.className = 'sent-message';
                  sentMsg.innerHTML = `
                      <p>${message}</p>
                      <small class="message-time">Just now</small>
                  `;
                  messageContainer.appendChild(sentMsg);
                  
                  // Clear input
                  messageInput.value = '';
                  
                  // Scroll to bottom
                  messageContainer.scrollTop = messageContainer.scrollHeight;
                  
                  // Open WhatsApp in new tab after a brief delay
                  setTimeout(() => {
                      const encodedMessage = encodeURIComponent(message);
                      window.open(`https://wa.me/${whatsappNumber}?text=${encodedMessage}`, '_blank');
                      
                      // Hide popup after sending
                      popup.style.display = 'none';
                  }, 500);
              }
          });
      });
  </script>

<?php endif; ?>

<?php include('lazy-includes/footer2.php'); ?>