<?php
// error_reporting(0);
require "../../config.php";

if (!isset($_SESSION['username'])) {
  header('location:logout');
}
if ($_SESSION['token'] !== "1e8789816530b40d8784c371d829db38") {
  header('location:../login');
}
if (!isset($_SESSION['LAST_ACTIVITY'])) {
  header('location:../login');
}
if (time() - $_SESSION['LAST_ACTIVITY'] > 300000) {
  $last = $_SERVER['REQUEST_URI'];
  header("location:../login?last={$last}");
}
$_SESSION['LAST_ACTIVITY'] = time();
function parseAmt($amt)
{
  $val = intval($amt);
  if ($val > 999999) {
    return substr_replace(substr_replace($val, ',', -3, 0), ',', -7, 0);
  } elseif ($val > 999) {
    return substr_replace($val, ',', -3, 0);
  } else {
    return $val;
  }
}

// if (($data['reserveBank'] == null || empty($data['reserveBank']) || $data['reserveBank'] == '[]') && !empty(json_decode($config['monnify'])[0])) {
//   $username = $data['username'];
//   $email = $data['email'];
//   $name = $data['name'];
//   try {
//     $apik = trim(json_decode($config['monnify'])[0]);
//     $seck = trim(json_decode($config['monnify'])[2]);
//     $apiCon = trim(json_decode($config['monnify'])[1]);
//     $au = base64_encode("{$apik}:{$seck}");

//     // Login to get the access token
//     $ch = curl_init();
//     curl_setopt($ch, CURLOPT_URL, "https://api.monnify.com/api/v1/auth/login/");
//     curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
//     curl_setopt($ch, CURLOPT_HEADER, FALSE);
//     curl_setopt($ch, CURLOPT_POST, TRUE);
//     curl_setopt($ch, CURLOPT_HTTPHEADER, array(
//       "Content-Type: application/json",
//       "Authorization: Basic " . $au
//     ));
//     $response = curl_exec($ch);
//     curl_close($ch);
//     $tk = json_decode($response)->responseBody->accessToken;

//     // Create the reserved account
//     $ch = curl_init();
//     curl_setopt($ch, CURLOPT_URL, "https://api.monnify.com/api/v2/bank-transfer/reserved-accounts");
//     curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
//     curl_setopt($ch, CURLOPT_HEADER, FALSE);
//     curl_setopt($ch, CURLOPT_POST, TRUE);
//     $tRef = md5($name . $email . time());
//     curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
//       "accountName" => $name,
//       "accountReference" => $tRef,
//       "currencyCode" => "NGN",
//       "contractCode" => $apiCon,
//       "customerName" => $name,
//       "customerEmail" => $email,
//       "getAllAvailableBanks" => false,
//       "preferredBanks" => ["50515", "232", "035", "070", "058"]
//     ]));

//     curl_setopt($ch, CURLOPT_HTTPHEADER, array(
//       "Content-Type: application/json",
//       "Authorization: Bearer " . $tk
//     ));
//     $response = curl_exec($ch);
//     curl_close($ch);

//     // Handle and store the response
//     // mysqli_query($con, "INSERT INTO error (det) VALUES ('$response')");
//     $response = json_decode($response, true);
//     $accounts = $response['responseBody']['accounts'];
//     $selectedData = [];
//     foreach ($accounts as $account) {
//       $selectedData[] = [
//         'bankCode' => $account['bankCode'],
//         'bankName' => $account['bankName'],
//         'accountNumber' => $account['accountNumber'],
//         'accountName' => $account['accountName']
//       ];
//     }
//     $genAccDet = json_encode($selectedData);
//     mysqli_query($con, "UPDATE users SET reserveBank = '$genAccDet' WHERE username = '$username'");
//   } catch (Throwable $e) {
//     $current_date = date('Y-m-d H:i:s');
//     $myfile = fopen("errorFile.txt", "a") or die("Unable to open file!");
//     fwrite($myfile, '\r\n ' . $current_date . ': ' . $e . '\r\n');
//     fclose($myfile);
//   }
// }
 

if (empty($_SESSION['data']['reserveBank'])) {
    $apik = trim(json_decode($config['monnify'])[0]);
    $seck = trim(json_decode($config['monnify'])[2]);
    $apiCon = trim(json_decode($config['monnify'])[1]);
    $au = base64_encode("{$apik}:{$seck}");
    $userN = $_SESSION['data']['username'];
    $userE = $_SESSION['data']['email'];
    $userFN = $_SESSION['data']['name'];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://api.monnify.com/api/v1/auth/login/',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_HTTPHEADER => array(
            'Authorization: Basic ' . $au,
            'Content-Type: application/json'
        ),
    ));
    $response = curl_exec($curl);
    curl_close($curl);
    

    if ($response) {
        $tk = json_decode($response)->responseBody->accessToken;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.monnify.com/api/v2/bank-transfer/reserved-accounts");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_HEADER, FALSE);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
            "accountName" => $userFN,
            "accountReference" => "dtp_$userE",
            "currencyCode" => "NGN",
            "contractCode" => $apiCon,
            "customerName" => $userFN,
            "customerEmail" => $userE,
            "getAllAvailableBanks" => false,
            "preferredBanks" => ["50515", "035", "232"]
        ]));

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer " . $tk
        ));
        $response = curl_exec($ch);
        curl_close($ch);

        $current_date = date('Y-m-d H:i:s');
        mysqli_query($con, "INSERT INTO error (name, det, timest) VALUES ('RESERVED ACCOUNT', '$response', '$current_date')");
        $response = json_decode($response, true);
        if ($response['requestSuccessful']) {
            $accounts = $response['responseBody']['accounts'];
            $accountReference = $response['responseBody']['accountReference'];
            $selectedData = [];
            foreach ($accounts as $account) {
                $selectedData[] = [
                    'bankCode' => $account['bankCode'],
                    'bankName' => $account['bankName'],
                    'accountNumber' => $account['accountNumber'],
                    'accountName' => $account['accountName'],
                    'reference' => $accountReference
                ];
            }
            $genAccDet = json_encode($selectedData);
            $resData =   json_encode($response['responseBody']);
            mysqli_query($con, "UPDATE users SET reserveBank = '$genAccDet', reserveData = '$resData' WHERE  username = '$userN'");
            $reservedCreated = true;
        }
    }
}
?>
<!DOCTYPE html>
<html style="width: 100%">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="<?= $config['site_name'] ?> We offer modern solutions for internet connection, We are here to always serve you">
  <meta name="author" content="<?= $config['site_name'] ?>">
  <title><?= $config['site_name'] ?></title>
  <!-- Favicon -->
  <link rel="icon" href="../assets/img/brand/favicon.png" type="image/png">
  <!-- Fonts -->
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/slick-loader@1.1.20/slick-loader.min.css">
  <!-- <script src="https://unpkg.com/slick-loader@1.1.20/slick-loader.min.js"></script> -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700">
  <!-- Icons -->
  <link rel="stylesheet" href="../assets/vendor/nucleo/css/nucleo.css" type="text/css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.13.0/css/all.min.css" rel="stylesheet">
  <!-- Page plugins -->
  <!-- Argon CSS -->
  <link rel="stylesheet" href="../assets/css/argon.css?v=1.2.0" type="text/css">

</head>

<body>
  <div style="position: fixed; bottom: 1%; left: 1%; z-index: 9999999">
    <a href="http://wa.me/234<?= substr(json_decode($config['whatsapp_num'])[0], 1); ?>" target="_blank"><img src="what.png" id="wImg" style="width: 50px; height: 50px;"></a>
  </div>
  <!-- Sidenav -->
  <nav class="sidenav navbar navbar-vertical  fixed-left  navbar-expand-xs navbar-light bg-white" id="sidenav-main">
    <div class="scrollbar-inner">
      <!-- Brand -->
      <div style="visibility: hidden;" class="sidenav-header  align-items-center">
        <a class="navbar-brand" href="javascript:void(0)">
          <img src="../assets/img/brand/blue.png" class="navbar-brand-img" alt="...">
        </a>
      </div>
      <div class="navbar-inner">
        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="sidenav-collapse-main">
          <!-- Nav items -->
          <ul class="navbar-nav">
            <li class="nav-item">
              <a class="nav-link active" href="index">
                <i class="ni ni-tv-2 text-primary"></i>
                <span class="nav-link-text">Dashboard</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="javascript:void(0);" onclick='$("#sub-m").slideToggle();' class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fa fa-wallet text-orange"></i>
                <span class="nav-link-text">Wallet Funding</span>

              </a>
              <ul id="sub-m" class="sub-menu children dropdown-menu">

                <li class="nav-item">
                  <a class="nav-link" href="manual">
                    <i class="ni ni-box-2 text-primary"></i>
                    <span class="nav-link-text">Manual Funding</span>
                  </a>
                </li>
                <?php 
                // Check if Monnify is enabled
                $monnify_enabled = !isset($config['monnify_enabled']) || $config['monnify_enabled'] != 'disabled';
                ?>
                
                <?php if ($monnify_enabled): ?>
                <li class="nav-item">
                  <a class="nav-link" href="auto">
                    <i class="ni ni-credit-card text-yellow"></i>
                    <span class="nav-link-text">ATM Automatic Funding (Monnify)</span>
                  </a>
                </li>
                <?php endif ?>
                
                <!--<li class="nav-item">
                  <a class="nav-link" href="paystack">
                    <i class="ni ni-credit-card text-yellow"></i>
                    <span class="nav-link-text">ATM Automatic Funding (Paystack)</span>
                  </a>
                </li>-->
                <li class="nav-item">
                  <a class="nav-link" href="auto2">
                    <i class="fa fa-pallet text-default"></i>
                    <span class="nav-link-text">Automatic (Direct)</span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="payvessel">
                    <i class="ni ni-credit-card text-success"></i>
                    <span class="nav-link-text">ATM Automatic Funding (Payvessel)</span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="payvessel_virtual">
                    <i class="fa fa-university text-info"></i>
                    <span class="nav-link-text">Virtual Account (Payvessel)</span>
                  </a>
                </li>
              </ul>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="transactions">
                <i class="ni ni-bullet-list-67 text-default"></i>
                <span class="nav-link-text">Transaction History</span>
              </a>
            </li>


            <li class="nav-item">
              <a class="nav-link" href="airtime">
                <i class="fa fa-phone text-primary"></i>
                <span class="nav-link-text">Airtime Top-Up</span>
              </a>
            </li>

            <!--<li class="nav-item">-->
            <!--  <a class="nav-link" href="airtime_bulk">-->
            <!--    <i class="fa fa-clone text-primary"></i>-->
            <!--    <span class="nav-link-text">Bulk Airtime Top-Up</span>-->
            <!--  </a>-->
            <!--</li>-->

            <li class="nav-item">
              <a class="nav-link" href="data_purchase">
                <i class="fa fa-rocket text-primary"></i>
                <span class="nav-link-text">Data Top-Up</span>
              </a>
            </li>


            <li class="nav-item">
              <a class="nav-link" href="bill">
                <i class="ni ni-building text-default"></i>
                <span class="nav-link-text">Electricity Payment</span>
              </a>
            </li>


            <li class="nav-item">
              <a class="nav-link" href="cable">
                <i class="fa fa-film text-info"></i>
                <span class="nav-link-text">Cable TV Subscription</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="a2c">
                <i class="ni ni-ui-04 text-pink"></i>
                <span class="nav-link-text">Airtime2Cash</span>
              </a>
            </li>



            <li class="nav-item">
              <a class="nav-link" href="checker">
                <i class="fa fa-graduation-cap text-primary"></i>
                <span class="nav-link-text">Result Checker</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="smile">
                <i class="fa fa-chart-pie text-pink"></i>
                <span class="nav-link-text">Smile Bundle</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="rec">
                <i class="fa fa-print text-primary"></i>
                <span class="nav-link-text">Recharge Card Printing</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="profile">
                <i class="ni ni-single-02 text-yellow"></i>
                <span class="nav-link-text">Profile</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="pricing">
                <i class="fa fa-book text-default"></i>
                <span class="nav-link-text">Pricing List</span>
              </a>
            </li>



            <li class="nav-item">
              <a class="nav-link" href="pin_manage">
                <i class="fa fa-lock text-info"></i>
                <span class="nav-link-text">PIN</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="https://<?= $config['site_link']; ?>/docs/">
                <i class="fa fa-code text-info"></i>
                <span class="nav-link-text">Developer's API</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="faq">
                <i class="fa fa-tag text-danger"></i>
                <span class="nav-link-text">FAQs</span>
              </a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="settings">
                <i class="ni ni-settings text-pink"></i>
                <span class="nav-link-text">Settings</span>
              </a>
            </li>

            <li class="nav-item" onclick="Swal.fire('You want to log out?','','question').then((res)=>{if(res.isConfirmed){
            window.location.replace('logout');
          }})">
              <a class="nav-link">
                <i class="ni ni-button-power text-pink"></i>
                <span class="nav-link-text">Logout</span>
              </a>
            </li>
          </ul>
          <!-- Divider -->
          <hr class="my-3">

        </div>
      </div>
    </div>
  </nav>
  <!-- Main content -->
  <div class="main-content" id="panel">
    <!-- Topnav -->
    <nav style="position: fixed; margin-top: -60px !important; z-index: 9999999; width: 100%; max-width: 100%; height: 60px;" class="navbar navbar-top navbar-expand navbar-dark bg-primary">
      <div class="container-fluid">
        <ul class="navbar-nav">
          <li class="nav-item dropdown">
            <a class="nav-link pr-0" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <div style="margin: 10px 20px auto auto;" class="media align-items-center">
                <span class="avatar avatar-sm rounded-circle">
                  <img alt="Image placeholder" src="../assets/img/theme/team-4.jpg">
                </span>
                <div class="media-body  ml-2  d-none d-lg-block">
                  <span class="mb-0 text-sm  font-weight-bold"><?= $data['name'] ?></span>
                </div>
              </div>
            </a>
            <div class="dropdown-menu  dropdown-menu-right ">
              <div class="dropdown-header noti-title">
                <h6 class="text-overflow m-0">Welcome!</h6>
              </div>
              <a href="profile" class="dropdown-item">
                <i class="ni ni-single-02"></i>
                <span>My profile</span>
              </a>
              <a href="settings" class="dropdown-item">
                <i class="ni ni-settings-gear-65"></i>
                <span>Settings</span>
              </a>
              <div class="dropdown-divider"></div>
              <a href="#!" onclick="Swal.fire('You want to log out?','','question').then((res)=>{if(res.isConfirmed){
              window.location.replace('logout');
            }})" class="dropdown-item">
                <i class="ni ni-user-run"></i>
                <span>Logout</span>
              </a>
            </div>
          </li>


        </ul>
        <!-- Search form -->

        <!-- Navbar links -->



        <center>
          <h3 class="text-white" style="font-family: arial; margin-top: 20px; ">Wallet Balance: <b>₦<?= parseAmt($data['bal']) ?></b></h3>
        </center>
        <li class="nav-item d-xl-none mr-3" style="margin-left: 60px;">
          <!-- Sidenav toggler -->
          <div class="md-3 sidenav-toggler sidenav-toggler-dark" data-action="sidenav-pin" data-target="#sidenav-main">
            <div class="sidenav-toggler-inner">
              <i class="sidenav-toggler-line"></i>
              <i class="sidenav-toggler-line"></i>
              <i class="sidenav-toggler-line"></i>
            </div>
          </div>
        </li>
      </div>
    </nav>
    <!-- Header -->
    <!-- Header -->
    <div class="header bg-primary pb-6" style="width: 100%; margin-bottom: 60px;">