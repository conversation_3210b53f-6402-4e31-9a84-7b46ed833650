<?php
// Comprehensive Payvessel Database Setup Script
include 'conn.php';

echo "<h2>Payvessel Database Setup</h2>";
echo "<p>Checking and adding required database columns...</p>";

$success = true;
$errors = [];
$added_columns = [];

// 1. Check and add columns to users table
echo "<h3>Users Table Setup:</h3>";

$userColumns = [
    'bvn' => 'VARCHAR(11) DEFAULT NULL',
    'nin' => 'VARCHAR(11) DEFAULT NULL', 
    'payvessel_accounts' => 'TEXT DEFAULT NULL'
];

foreach ($userColumns as $column => $definition) {
    try {
        // Check if column exists
        $result = mysqli_query($con, "SHOW COLUMNS FROM users LIKE '$column'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ Column '$column' already exists in users table</p>";
        } else {
            // Add column
            $sql = "ALTER TABLE users ADD COLUMN $column $definition";
            if (mysqli_query($con, $sql)) {
                echo "<p style='color: green;'>✅ Added column '$column' to users table</p>";
                $added_columns[] = "users.$column";
            } else {
                $errors[] = "Failed to add column '$column' to users table: " . mysqli_error($con);
                $success = false;
            }
        }
    } catch (Exception $e) {
        $errors[] = "Error with column '$column': " . $e->getMessage();
        $success = false;
    }
}

// 2. Check and add columns to config table
echo "<h3>Config Table Setup:</h3>";

$configColumns = [
    'payvessel' => 'TEXT DEFAULT NULL'
];

foreach ($configColumns as $column => $definition) {
    try {
        // Check if column exists
        $result = mysqli_query($con, "SHOW COLUMNS FROM config LIKE '$column'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ Column '$column' already exists in config table</p>";
        } else {
            // Add column
            $sql = "ALTER TABLE config ADD COLUMN $column $definition";
            if (mysqli_query($con, $sql)) {
                echo "<p style='color: green;'>✅ Added column '$column' to config table</p>";
                $added_columns[] = "config.$column";
            } else {
                $errors[] = "Failed to add column '$column' to config table: " . mysqli_error($con);
                $success = false;
            }
        }
    } catch (Exception $e) {
        $errors[] = "Error with column '$column': " . $e->getMessage();
        $success = false;
    }
}

// 3. Verify final structure
echo "<h3>Final Verification:</h3>";

// Check users table
$result = mysqli_query($con, "DESCRIBE users");
$userColumnsFinal = [];
while ($row = mysqli_fetch_assoc($result)) {
    $userColumnsFinal[] = $row['Field'];
}

$requiredUserColumns = ['bvn', 'nin', 'payvessel_accounts'];
foreach ($requiredUserColumns as $column) {
    if (in_array($column, $userColumnsFinal)) {
        echo "<p style='color: green;'>✅ '$column' column verified in users table</p>";
    } else {
        echo "<p style='color: red;'>❌ '$column' column missing in users table</p>";
        $success = false;
    }
}

// Check config table
$result = mysqli_query($con, "DESCRIBE config");
$configColumnsFinal = [];
while ($row = mysqli_fetch_assoc($result)) {
    $configColumnsFinal[] = $row['Field'];
}

if (in_array('payvessel', $configColumnsFinal)) {
    echo "<p style='color: green;'>✅ 'payvessel' column verified in config table</p>";
} else {
    echo "<p style='color: red;'>❌ 'payvessel' column missing in config table</p>";
    $success = false;
}

// Show any errors
if (!empty($errors)) {
    echo "<h3>Errors:</h3>";
    foreach ($errors as $error) {
        echo "<p style='color: red;'>❌ $error</p>";
    }
}

// Show added columns
if (!empty($added_columns)) {
    echo "<h3>Added Columns:</h3>";
    foreach ($added_columns as $column) {
        echo "<p style='color: blue;'>➕ $column</p>";
    }
}

// Final status
if ($success) {
    echo "<h3 style='color: green;'>🎉 Database setup completed successfully!</h3>";
    echo "<p>All required columns are now available for Payvessel KYC and virtual accounts.</p>";
} else {
    echo "<h3 style='color: red;'>⚠️ Database setup completed with errors</h3>";
    echo "<p>Please check the errors above and run the setup again if needed.</p>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Configure Payvessel credentials in admin panel</li>";
echo "<li>Test KYC verification: <a href='web/app-/payvessel_kyc.php'>Payvessel KYC</a></li>";
echo "<li>Test virtual accounts: <a href='web/app-/payvessel_virtual_account.php'>Virtual Accounts</a></li>";
echo "<li>Set up webhook URL in Payvessel dashboard</li>";
echo "</ol>";

mysqli_close($con);
?> 