<?php
// Simple test to verify webhook endpoint is accessible
echo "Webhook endpoint is accessible!";
echo "<br>Time: " . date('Y-m-d H:i:s');
echo "<br>Server: " . $_SERVER['SERVER_NAME'];
echo "<br>Method: " . $_SERVER['REQUEST_METHOD'];

// Test database connection
try {
    include 'conn.php';
    echo "<br>Database connection: ✅ Success";
    
    // Test config table
    $config = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM config"));
    if ($config) {
        echo "<br>Config table: ✅ Success";
    } else {
        echo "<br>Config table: ❌ Failed";
    }
    // this is the test webhook take note.
    // Test users table
    $users = mysqli_query($con, "SELECT COUNT(*) as count FROM users");
    if ($users) {
        $userCount = mysqli_fetch_assoc($users)['count'];
        echo "<br>Users table: ✅ Success (Count: $userCount)";
    } else {
        echo "<br>Users table: ❌ Failed";
    }
    
    // Test transactions table
    $transactions = mysqli_query($con, "SELECT COUNT(*) as count FROM transactions");
    if ($transactions) {
        $transCount = mysqli_fetch_assoc($transactions)['count'];
        echo "<br>Transactions table: ✅ Success (Count: $transCount)";
    } else {
        echo "<br>Transactions table: ❌ Failed";
    }
    
} catch (Exception $e) {
    echo "<br>Database connection: ❌ Failed - " . $e->getMessage();
}

// Test file permissions
$webhookFile = 'payvesselWebHook.php';
if (file_exists($webhookFile)) {
    echo "<br>Webhook file exists: ✅ Success";
    if (is_readable($webhookFile)) {
        echo "<br>Webhook file readable: ✅ Success";
    } else {
        echo "<br>Webhook file readable: ❌ Failed";
    }
} else {
    echo "<br>Webhook file exists: ❌ Failed";
}

// Test PHP error reporting
echo "<br>PHP Version: " . phpversion();
echo "<br>Error Reporting: " . error_reporting();
echo "<br>Display Errors: " . ini_get('display_errors');

// Test if we can write to error log
error_log("Webhook test - " . date('Y-m-d H:i:s'));
echo "<br>Error logging: ✅ Tested";

echo "<br><br>Webhook endpoint test completed!";
?> 