const CACHE_NAME = 'sharesub-cache-v2';
const urlsToCache = [
  '/',
  '/login.php',
  '/signup.php',
  '/welcome.php',
  '/assets/css/style.css',
  '/assets/js/pwa-services.js',
  '/assets/img/logo.png',
  '/assets/img/favicon32.png',
  '/assets/img/favicon180.png',
  '/assets/img/favicon16.png',
  '/assets/img/sharesub-logo.png',
  '/offline.html'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(name => name !== CACHE_NAME).map(name => caches.delete(name))
      );
    })
  );
});

self.addEventListener('fetch', event => {
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request).catch(() => caches.match('/offline.html'))
    );
  } else {
    event.respondWith(
      caches.match(event.request)
        .then(response => response || fetch(event.request))
    );
  }
}); 