<?php
session_start();
include 'conn.php';

echo "<h2>KYC Status Debug</h2>";

// Check if user is logged in
if (!isset($_SESSION['data']['username'])) {
    echo "<p style='color: red;'>❌ User not logged in</p>";
    echo "<p>Please login first to check KYC status.</p>";
    exit();
}

$username = $_SESSION['data']['username'];
echo "<p><strong>Username:</strong> " . htmlspecialchars($username) . "</p>";

// Get user data from database
$data = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM users WHERE username = '$username'"));

if (!$data) {
    echo "<p style='color: red;'>❌ User not found in database</p>";
    exit();
}

echo "<h3>Database KYC Status:</h3>";
echo "<p><strong>BVN:</strong> " . (empty($data['bvn']) ? 'NOT SET' : htmlspecialchars($data['bvn'])) . "</p>";
echo "<p><strong>NIN:</strong> " . (empty($data['nin']) ? 'NOT SET' : htmlspecialchars($data['nin'])) . "</p>";

echo "<h3>Session Data:</h3>";
echo "<p><strong>Session Username:</strong> " . htmlspecialchars($_SESSION['data']['username']) . "</p>";
echo "<p><strong>Session Email:</strong> " . htmlspecialchars($_SESSION['data']['email']) . "</p>";
echo "<p><strong>Session Name:</strong> " . htmlspecialchars($_SESSION['data']['name']) . "</p>";

echo "<h3>KYC Requirements Check:</h3>";
if (!empty($data['bvn']) && !empty($data['nin'])) {
    echo "<p style='color: green;'>✅ KYC COMPLETED - Both BVN and NIN are set</p>";
    echo "<p>You can now create virtual accounts!</p>";
} elseif (!empty($data['bvn']) && empty($data['nin'])) {
    echo "<p style='color: orange;'>⚠️ PARTIAL KYC - BVN is set but NIN is missing</p>";
    echo "<p>You need to add NIN to complete KYC.</p>";
} elseif (empty($data['bvn']) && !empty($data['nin'])) {
    echo "<p style='color: orange;'>⚠️ PARTIAL KYC - NIN is set but BVN is missing</p>";
    echo "<p>You need to add BVN to complete KYC.</p>";
} else {
    echo "<p style='color: red;'>❌ NO KYC - Both BVN and NIN are missing</p>";
    echo "<p>You need to complete KYC verification.</p>";
}

echo "<h3>Database Query Debug:</h3>";
$query = "SELECT username, bvn, nin FROM users WHERE username = '$username'";
echo "<p><strong>Query:</strong> $query</p>";

$result = mysqli_query($con, $query);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "<p><strong>Result:</strong></p>";
    echo "<pre>" . print_r($row, true) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ Query failed: " . mysqli_error($con) . "</p>";
}

echo "<hr>";
echo "<h3>Quick Actions:</h3>";
echo "<p><a href='web/app-/payvessel_kyc.php'>Complete KYC</a></p>";
echo "<p><a href='test_payvessel_api.php'>Test Payvessel API</a></p>";
echo "<p><a href='web/app-/payvessel_virtual_account.php'>Virtual Accounts</a></p>";

mysqli_close($con);
?> 