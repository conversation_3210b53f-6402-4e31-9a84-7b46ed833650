/* Custom color overrides for SHARESUB
 * Changes all red color instances to match logo color
 */

:root {
  /* Override red color variables with logo color */
  --finwallapp-red: #2c74b4 !important;
  --finwallapp-theme-color: #2c74b4 !important;
  --bs-red: #2c74b4 !important;
  --bs-danger: #2c74b4 !important;
  /* Override primary color with blue */
  --bs-primary: #2c74b4 !important;
  --bs-primary-hover: #235e91 !important; /* Slightly darker shade for hover states */
  --bs-primary-rgb: 44, 116, 180 !important; /* RGB values for rgba() usage */
}

/* Primary Button Overrides */
.btn-primary {
  background-color: #2c74b4 !important;
  border-color: #2c74b4 !important;
}

.btn-primary:hover, 
.btn-primary:focus, 
.btn-primary:active {
  background-color: #235e91 !important;
  border-color: #235e91 !important;
}

.btn-outline-primary {
  color: #2c74b4 !important;
  border-color: #2c74b4 !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
  background-color: #2c74b4 !important;
  color: #fff !important;
}

/* Button overrides for danger/red */
.btn-danger {
  background-color: #2c74b4 !important;
  border-color: #2c74b4 !important;
}

.btn-outline-danger {
  color: #2c74b4 !important;
  border-color: #2c74b4 !important;
}

.btn-outline-danger:hover {
  background-color: #2c74b4 !important;
  color: #fff !important;
}

/* Card styles with primary color */
.card.bg-primary, 
.card.border-primary,
.card-header.bg-primary,
.card .bg-primary,
.card-body.bg-primary,
.footer.bg-primary {
  background-color: #2c74b4 !important;
  border-color: #2c74b4 !important;
}

/* Text color overrides */
.text-primary {
  color: #2c74b4 !important;
}

.text-danger {
  color: #2c74b4 !important;
}

/* Border color overrides */
.border-primary {
  border-color: #2c74b4 !important;
}

.border-danger {
  border-color: #2c74b4 !important;
}

/* Background color overrides - comprehensive targeting for all pages */
.bg-primary,
div.bg-primary,
span.bg-primary,
section.bg-primary,
header.bg-primary,
footer.bg-primary,
nav.bg-primary,
aside.bg-primary,
article.bg-primary,
.card .bg-primary,
[class*="bg-primary"],
[class^="bg-primary"],
.data-page .bg-primary,
.airtime-page .bg-primary,
.electricity-page .bg-primary,
.cabletv-page .bg-primary,
.a2c-page .bg-primary,
.data-card-page .bg-primary,
.monnify-kyc-page .bg-primary {
  background-color: #2c74b4 !important;
}

/* Handle various rgba() variations of primary color */
[style*="background-color: rgba(13, 110, 253"],
[style*="background-color: rgba(0, 123, 255"] {
  background-color: rgba(44, 116, 180, 0.8) !important;
}

/* Primary backgrounds with opacity */
.bg-primary-light,
.bg-primary-subtle,
.bg-primary-transparent {
  background-color: rgba(44, 116, 180, 0.2) !important;
}

.bg-danger {
  background-color: #2c74b4 !important;
}

/* Progress bars and other components */
.progress-bar-danger {
  background-color: #2c74b4 !important;
}

.progress-bar-primary,
.progress-bar.bg-primary,
.progress .bg-primary {
  background-color: #2c74b4 !important;
}

/* Alert overrides */
.alert-danger {
  background-color: rgba(44, 116, 180, 0.1) !important;
  border-color: rgba(44, 116, 180, 0.2) !important;
  color: #2c74b4 !important;
}

.alert-primary {
  background-color: rgba(44, 116, 180, 0.1) !important;
  border-color: rgba(44, 116, 180, 0.2) !important;
  color: #2c74b4 !important;
}

/* Nav tabs/pills with primary color */
.nav-pills .nav-link.active,
.nav-tabs .nav-link.active {
  background-color: #2c74b4 !important;
  color: white !important;
}

.nav-pills .nav-link:not(.active):hover,
.nav-tabs .nav-link:not(.active):hover {
  color: #2c74b4 !important;
}

/* Override any specific instances of hardcoded colors */
[style*="#F73563"] {
  color: #2c74b4 !important;
}

[style*="#FF345E"] {
  color: #2c74b4 !important;
}

[style*="background-color: #F73563"], 
[style*="background-color: #FF345E"] {
  background-color: #2c74b4 !important;
}

/* Override primary color inline styles */
[style*="#007bff"],
[style*="#0d6efd"] {
  color: #2c74b4 !important;
}

[style*="background-color: #007bff"],
[style*="background-color: #0d6efd"] {
  background-color: #2c74b4 !important;
}

/* Page-specific overrides for the pages mentioned */
body[data-page="data"] .bg-primary,
body[data-page="airtime"] .bg-primary,
body[data-page="electricity"] .bg-primary,
body[data-page="cabletv"] .bg-primary,
body[data-page="a2c"] .bg-primary,
body[data-page="data-card"] .bg-primary,
body[data-page="monnify_kyc"] .bg-primary {
  background-color: #2c74b4 !important;
}

/* Make sure header and card backgrounds are correctly styled */
.header.bg-primary,
.card.bg-primary,
.card > .bg-primary,
.card > * > .bg-primary {
  background-color: #2c74b4 !important;
} 