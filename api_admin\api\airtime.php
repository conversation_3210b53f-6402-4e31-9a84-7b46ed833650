<?php
$actual_link = $_SERVER['REMOTE_ADDR'];
//if ($actual_link != '198.187.31.83') die('Invalid REQUEST');
include "../../conn.php";
file_put_contents('airtimeRes.txt', "response");
if ($_SERVER["REQUEST_METHOD"] == "GET") {
	die(json_encode(array('error' => true, 'desc' => 'Unsupported Method')));
}
$headers = apache_request_headers();

if (!isset($_POST['phone_number']) || empty($_POST['phone_number']) || strlen($_POST['phone_number']) != 11) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid phone_number')));
}
if (!isset($_POST['network_id']) || empty($_POST['network_id'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid network_id')));
}

if (!isset($_POST['amount']) || empty($_POST['amount']) || intval($_POST['amount']) == 0) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid amount')));
}

if (!isset($_POST['type']) || empty($_POST['type'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid type')));
}

if (strtolower($_POST['type']) != 'vtu' && strtolower($_POST['type']) != 'sns') {
	die(json_encode(array('error' => true, 'desc' => 'Invalid type. Type must be either SNS or VTU')));
}

$phone_number = mysqli_real_escape_string($con, $_POST['phone_number']);
$network_id = mysqli_real_escape_string($con, $_POST['network_id']);
$amount = intval(mysqli_real_escape_string($con, $_POST['amount']));
$type = strtolower(mysqli_real_escape_string($con, $_POST['type']));
$ported_number = (isset($_POST['ported_number']) && $_POST['ported_number'] == 'true') ? true : false;
if ($amount == 0) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid amount')));
}

function randomNumber($length)
{
	$result = '';
	for ($i = 0; $i < $length; $i++) {
		$result .= mt_rand(0, 9);
	}
	return 'AIR' . $result;
}

$network = [];
$nets = mysqli_query($con, "SELECT * FROM networks");
while ($net = mysqli_fetch_assoc($nets)) {
	if ($net['id'] == $network_id) {
		$network[0] = $net['id'];
		$network[1] = $net['name'];
	}
}
if ($network == []) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid network_id')));
}



$net_id = $network[0];

$config = mysqli_fetch_assoc(mysqli_query($con, "SELECT airtime_state FROM config"));
$airtime_state = $config['airtime_state'];


if ($airtime_state != 'true') {
	die(json_encode(array('error' => true, 'desc' => 'AIRTIME NOT AVAILABLE AT THE MOMENT')));
}

$airtime_conf = mysqli_fetch_array(mysqli_query($con, "SELECT * FROM airtime_config WHERE network_id = '$net_id'"));

$ttype = 'AIRTIME PURCHASE';
$t_desc = $network[1] . " AIRTIME OF " . $amount . " TO " . $phone_number;
$status = 'pending';
$t_date = $current_date;
$ref = randomNumber(15);
$channel = $network[1];
$platForms;

if ($type == 'sns') {
	$platForms = $airtime_conf['source_sns'];
} else {
	$platForms = $airtime_conf['source_vtu'];
}



$netsToken = mysqli_query($con, "SELECT * FROM apikey WHERE platform='$platForms' AND types='airtime' ");
$tokenKey = mysqli_fetch_assoc($netsToken);
if (!$tokenKey) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid apikey')));
}
$apiLink = $tokenKey['apiLink'];
$apiKey = $tokenKey['apiKey'];
$secretkey = $tokenKey['secretkey'];

if (in_array($platForms, ['superjara', 'gongoz', 'maskawa', 'husmodata', 'gladtidingsdata', 'legitdataway', 'n3tdata', 'bilalsadasub', 'edata', 'vtpass'])) {
	$platform = strtoupper($platForms);

	$resp = json_decode(airtimeFromSource($ref, strtolower($network[1]), $amount, $phone_number, $apiLink, $apiKey, $secretkey));

	if ((isset($resp->Status) && ($resp->Status == 'successful' || $resp->Status == 'processing')) || (isset($resp->status) && ($resp->status == 'success' || $resp->status->response == 'successful' || $resp->status == 'successful')) || (isset($resp->code) && ($resp->code == '000' || $resp->code == '099'))) {

		$status = 'success';
		$resps = mysqli_real_escape_string($con, json_encode($resp));
		mysqli_query($con, "INSERT INTO api_transactions (type, t_desc, amount, amount_before, amount_after, username, status, t_date, ref, channel, platform) VALUES ('$ttype', '$t_desc', '$amount', '', '', '', '$status', '$t_date', '$ref', '$channel', '$platform')");
		die(json_encode(array('error' => false, 'status' => 202, 'desc' => $t_desc . ' Placed Successfully', 'ref' => $ref)));
	} else {
		$status = 'failed';
		$resps = mysqli_real_escape_string($con, json_encode($resp));
		mysqli_query($con, "INSERT INTO api_transactions (type, detail, t_desc, amount, amount_before, amount_after, username, status, t_date, ref, channel, platform) VALUES ('$ttype', '$resps', '$t_desc', '$amount', '', '', '', '$status', '$t_date', '$ref', '$channel', '$platform')");

		die(json_encode(array('error' => true, 'status' => 400, 'desc' => 'Transaction not completed', 'ref' => $ref)));
	}
}



function airtimeFromSource($ref, $net, $amount, $phone, $apiLink, $apiKey, $secretkey)
{
	$platforms1 = ['superjara', 'gongoz', 'maskawasub', 'husmodata', 'gladtidingsdata'];
	$platforms2 = ['legitdataway', 'n3tdata', 'bilalsadasub'];
	$platforms3 = ['edata'];
	$platforms4 = ['vtpass'];

	if (checkPlatforms1($apiLink, $platforms1)) {
		global $net_id;
		$networks = ['mtn' => 1, 'glo' => 2, '9mobile' => 3, 'airtel' => 4];
		$net = strtolower($net);
		$net_id = $networks[$net] ?? null;
		$pl = intval($amount);
		$js = json_encode(['network' => $net_id, 'mobile_number' => $phone, 'amount' => $pl, 'Ported_number' => true, 'airtime_type' => 'VTU']);
		$payload = ['Authorization: Token ' . $apiKey, 'Content-Type: application/json'];
	} elseif (checkPlatforms2($apiLink, $platforms2)) {
		global $net_id;
		$randomString = 'airtime_' . mt_rand(10000000000, 99999999999);
		$networks = ['mtn' => 1, 'glo' => 3, '9mobile' => 4, 'airtel' => 3];
		$net = strtolower($net);
		$net_id = $networks[$net] ?? null;
		$pl = intval($amount);
		$js = json_encode(['network' => $net_id, 'phone' => $phone, 'amount' => $pl, 'bypass' => true, 'request-id' => $randomString,'plan_type' => 'VTU']);
		$payload = ['Authorization: Token ' . $apiKey, 'Content-Type: application/json'];
	} elseif (checkPlatforms2($apiLink, $platforms3)) {
		global $net_id;
		$networks = ['mtn' => 1, 'glo' => 3, '9mobile' => 4, 'airtel' => 2];
		$net = strtolower($net);
		$net_id = $networks[$net] ?? null;
		$pl = intval($amount);
		$js = json_encode(['network_id' => $net_id, 'phone' => $phone, 'amount' => $pl, 'type' => 'VTU']);
		$payload = ['Authorization: Bearer ' . $apiKey, 'Content-Type: application/json'];
	} elseif (checkPlatforms4($apiLink, $platforms4)) {
		global $net_id;
		$net = $net_id == 4 ? 'etisalat' : $net;
		$net = strtolower($net);
		$ref = randomNumber(15);
		$pl = intval($amount);
		$js = ['request_id' => $ref, 'serviceID' => $net, 'amount' => $pl, 'phone' => $phone];
		$payload = ['api-key: ' . $apiKey, 'secret-key: ' . $secretkey];
	} else {
		http_response_code(400);
		die(json_encode(['error' => true, 'desc' => 'Invalid platform' . $apiLink]));
	}

	$curl = curl_init();
	curl_setopt_array($curl, array(
		CURLOPT_URL => $apiLink,
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => $js,
		CURLOPT_HTTPHEADER => $payload,
	));
	$response = curl_exec($curl);
	curl_close($curl);
	file_put_contents('airtimeRes.txt', "$response");
	return $response;
}


function checkPlatforms1($apiLink, $platforms1)
{
	foreach ($platforms1 as $platform) {
		if (strpos($apiLink, $platform) !== false) {
			return true;
		}
	}
	return false;
}

function checkPlatforms2($apiLink, $platforms2)
{
	foreach ($platforms2 as $platform) {
		if (strpos($apiLink, $platform) !== false) {
			return true;
		}
	}
	return false;
}

function checkPlatforms3($apiLink, $platforms3)
{
	foreach ($platforms3 as $platform) {
		if (strpos($apiLink, $platform) !== false) {
			return true;
		}
	}
	return false;
}


function checkPlatforms4($apiLink, $platforms4)
{
	foreach ($platforms4 as $platform) {
		if (strpos($apiLink, $platform) !== false) {
			return true;
		}
	}
	return false;
}
