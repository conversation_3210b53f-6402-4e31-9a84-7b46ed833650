{"version": 3, "sources": ["pricing-plan.scss"], "names": [], "mappings": "AAQA;EACE,gCAAgC,EAAA;;AAElC;EACE,gBAAgB;EAChB,mBAAmB,EAAA;;AAErB;EACE,iBAAiB;EACjB,mBAAmB,EAAA;;AAGrB;EACE,YAAY;EACZ,mBAAmB;EACnB,mBAAmB;EACnB,kBAAkB;EAClB,4BAAoB;EAApB,oBAAoB,EAAA;EALtB;IAfE,kDAAkD,EAAA;EAepD;IAfE,kDAAkD,EAAA;EAepD;IAfE,kDAAkD;IAiChD,oCAA4B;YAA5B,4BAA4B,EAAA;EAlBhC;IAsBI,iBAAiB;IACjB,oBAAoB,EAAA;;AAMtB;EACE,eAAe;EACf,WAAW;EACX,mBAAmB;EACnB,mBAAmB,EAAA;;AAGrB;EACE,eAAe;EACf,WAAW;EACX,iBAAiB;EACjB,mBAAmB,EAAA;;AAIrB;EACE,qBAAqB;EACrB,WAAW;EACX,YAAY;EACZ,eAAe;EACf,cAAc;EACd,mBAAmB,EAAA;EAEnB;IACE,cAzEkB,EAAA;EA4EpB;IACE,cA5EgB,EAAA;EA+ElB;IACE,cA/EuB,EAAA;;AAmF3B;EACE,gBAAgB;EAChB,eAAe;EACf,eAAe;EACf,iBAAiB;EACjB,mBAAmB;EACnB,cAAc,EAAA;;AAIhB;EACE,WAAW;EACX,eAAe;EACf,iBAAiB;EACjB,YAAY;EACZ,YAAY;EACZ,qBAAqB;EACrB,4BAAoB;EAApB,oBAAoB;EACpB,kBAAkB;EAClB,oBAAa;EAAb,aAAa;EACb,yBAAmB;UAAnB,mBAAmB;EACnB,iBAAiB;EACjB,kBAAkB;EAClB,wBAAuB;UAAvB,uBAAuB,EAAA;EAEvB;IACE,yBA/GkB;IAgHlB,WAAW,EAAA;IAFb;MAKI,6BAAkD,EAAA;IALtD;MASI,kCAA0B;cAA1B,0BAA0B;MAC1B,gBAAgB,EAAA;EAIpB;IACE,yBA5HgB;IA6HhB,WAAW,EAAA;IAFb;MAKI,6BAAgD,EAAA;IALpD;MASI,kCAA0B;cAA1B,0BAA0B;MAC1B,gBAAgB,EAAA;EAIpB;IACE,yBAzIuB;IA0IvB,WAAW,EAAA;IAFb;MAKI,6BAAuD,EAAA;IAL3D;MASI,kCAA0B;cAA1B,0BAA0B;MAC1B,gBAAgB,EAAA", "file": "pricing-plan.css", "sourcesContent": ["$colorPlanBasic: #fe397a;\n$colorPlanPro: #10bb87;\n$colorPlanEnterprise: #5d78ff;\n\n@mixin boxShadow {\n  box-shadow: 0 2px 40px 0 rgba(205, 205, 205, 0.55);\n}\n\nbody {\n  font-family: '<PERSON><PERSON>', sans-serif;\n}\n.pricing-table-subtitle {\n  margin-top: 68px;\n  font-weight: normal;\n}\n.pricing-table-title {\n  font-weight: bold;\n  margin-bottom: 68px;\n}\n\n.pricing-card {\n  border: none;\n  border-radius: 10px;\n  margin-bottom: 40px;\n  text-align: center;\n  transition: all 0.6s;\n\n  &:hover {\n    @include boxShadow();\n  }\n\n  &.pricing-card-highlighted {\n    @include boxShadow();\n  }\n\n  &:hover {\n    @include boxShadow();\n\n    transform: translateY(-10px);\n  }\n\n  .card-body {\n    padding-top: 55px;\n    padding-bottom: 62px;\n  }\n}\n\n.pricing-plan {\n\n  &-title {\n    font-size: 20px;\n    color: #000;\n    margin-bottom: 11px;\n    font-weight: normal;\n  }\n\n  &-cost {\n    font-size: 50px;\n    color: #000;\n    font-weight: bold;\n    margin-bottom: 29px;\n\n  }\n  \n  &-icon {\n    display: inline-block;\n    width: 40px;\n    height: 40px;\n    font-size: 40px;\n    line-height: 1;\n    margin-bottom: 24px;\n\n    .pricing-plan-basic & {\n      color: $colorPlanBasic;\n    }\n\n    .pricing-plan-pro & {\n      color: $colorPlanPro;\n    }\n\n    .pricing-plan-enterprise & {\n      color: $colorPlanEnterprise;\n    }\n  }\n\n  &-features {\n    list-style: none;\n    padding-left: 0;\n    font-size: 14px;\n    line-height: 2.14;\n    margin-bottom: 35px;\n    color: #303132;\n\n  }\n\n  &-purchase-btn {\n    color: #000;\n    font-size: 16px;\n    font-weight: bold;\n    width: 145px;\n    height: 45px;\n    border-radius: 22.5px;\n    transition: all 0.4s;\n    position: relative;\n    display: flex;\n    align-items: center;\n    margin-left: auto;\n    margin-right: auto;\n    justify-content: center;\n\n    .pricing-plan-basic & {\n      background-color: $colorPlanBasic;\n      color: #fff;\n\n      &:hover {\n        box-shadow: 0 3px 0 0 darken($colorPlanBasic, 25%);\n      }\n\n      &:active {\n        transform: translateY(3px);\n        box-shadow: none;\n      }\n    }\n\n    .pricing-plan-pro & {\n      background-color: $colorPlanPro;\n      color: #fff;\n\n      &:hover {\n        box-shadow: 0 3px 0 0 darken($colorPlanPro, 15%);\n      }\n\n      &:active {\n        transform: translateY(3px);\n        box-shadow: none;\n      }\n    }\n\n    .pricing-plan-enterprise & {\n      background-color: $colorPlanEnterprise;\n      color: #fff;\n\n      &:hover {\n        box-shadow: 0 3px 0 0 darken($colorPlanEnterprise, 15%);\n      }\n\n      &:active {\n        transform: translateY(3px);\n        box-shadow: none;\n      }\n    }\n  }\n}\n"]}