<?php
session_start();

echo "<h2>Session Debug Information</h2>";

echo "<h3>All Session Variables:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Common Session Variables:</h3>";
$commonVars = [
    'username' => $_SESSION['username'] ?? 'NOT SET',
    'data.username' => $_SESSION['data']['username'] ?? 'NOT SET',
    'data.email' => $_SESSION['data']['email'] ?? 'NOT SET',
    'data.name' => $_SESSION['data']['name'] ?? 'NOT SET',
    'email' => $_SESSION['email'] ?? 'NOT SET',
    'admin' => $_SESSION['admin'] ?? 'NOT SET',
    'role' => $_SESSION['role'] ?? 'NOT SET'
];

foreach ($commonVars as $var => $value) {
    $color = $value === 'NOT SET' ? 'red' : 'green';
    echo "<p style='color: $color;'>✅ $var: $value</p>";
}

echo "<hr>";
echo "<h3>Quick Links:</h3>";
echo "<p><a href='test_payvessel_api.php'>Test Payvessel API</a></p>";
echo "<p><a href='web/app-/payvessel_virtual_account.php'>Virtual Accounts</a></p>";
echo "<p><a href='web/app-/payvessel_kyc.php'>Payvessel KYC</a></p>";
?> 