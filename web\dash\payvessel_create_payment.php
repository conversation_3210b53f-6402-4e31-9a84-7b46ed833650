<?php
require "../../config.php";

// Check authentication
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get POST data
$amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$name = isset($_POST['name']) ? trim($_POST['name']) : '';
$phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';

// Validate inputs
if ($amount < 100 || $amount > 5000) {
    echo json_encode(['success' => false, 'message' => 'Amount must be between ₦100 and ₦5000']);
    exit;
}

if (empty($email) || empty($name) || empty($phone)) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

// Get Payvessel configuration
$payvessel_config = json_decode($config['payvessel'], true);
if (empty($payvessel_config) || !is_array($payvessel_config) || count($payvessel_config) < 2 || empty($payvessel_config[0]) || empty($payvessel_config[1])) {
    echo json_encode(['success' => false, 'message' => 'Payvessel is not configured']);
    exit;
}

try {
    // Calculate total amount with charge
    $charge = $amount * 0.015; // 1.5% charge
    $total_amount = $amount + $charge;
    
    // Generate unique reference
    $reference = 'PV_' . time() . '_' . rand(1000, 9999);
    
    // Prepare payment data
    $payment_data = [
        'amount' => $total_amount * 100, // Convert to kobo
        'currency' => 'NGN',
        'reference' => $reference,
        'customer' => [
            'name' => $name,
            'email' => $email,
            'phone' => $phone
        ],
        'metadata' => [
            'user_id' => $data['id'],
            'username' => $data['username'],
            'original_amount' => $amount,
            'charge' => $charge
        ],
        'callback_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/web/dash/payvessel_callback.php',
        'return_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/web/dash/payvessel_success.php'
    ];
    
    // Make API call to Payvessel
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.payvessel.com/pms/api/external/request/transaction/');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payment_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'api-key: ' . $payvessel_config[0],
        'api-secret: Bearer ' . $payvessel_config[1]
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200) {
        error_log("Payvessel API Error: HTTP $http_code - $response");
        echo json_encode(['success' => false, 'message' => 'Failed to create payment. Please try again.']);
        exit;
    }
    
    $result = json_decode($response, true);
    
    if (!$result || !isset($result['status']) || $result['status'] !== true) {
        error_log("Payvessel API Error: " . $response);
        echo json_encode(['success' => false, 'message' => 'Failed to create payment. Please try again.']);
        exit;
    }
    
    // Store transaction in database
    $transaction_data = [
        'user_id' => $data['id'],
        'username' => $data['username'],
        'amount' => $amount,
        'charge' => $charge,
        'total_amount' => $total_amount,
        'reference' => $reference,
        'payment_method' => 'payvessel',
        'status' => 'pending',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $insert_query = "INSERT INTO transactions (user_id, username, amount, charge, total_amount, reference, payment_method, status, created_at) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($con, $insert_query);
    mysqli_stmt_bind_param($stmt, 'issddsss', 
        $transaction_data['user_id'],
        $transaction_data['username'],
        $transaction_data['amount'],
        $transaction_data['charge'],
        $transaction_data['total_amount'],
        $transaction_data['reference'],
        $transaction_data['payment_method'],
        $transaction_data['status'],
        $transaction_data['created_at']
    );
    
    if (!mysqli_stmt_execute($stmt)) {
        error_log("Database Error: " . mysqli_error($con));
        echo json_encode(['success' => false, 'message' => 'Failed to save transaction']);
        exit;
    }
    
    // Return success with payment URL
    echo json_encode([
        'success' => true,
        'payment_url' => $result['data']['authorization_url'] ?? $result['data']['checkout_url'],
        'reference' => $reference
    ]);
    
} catch (Exception $e) {
    error_log("Payvessel Payment Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred. Please try again.']);
}
?> 