<?php 
if (isset($_POST['kyc'])) {
	include "../../config.php";
	$nin = $_POST['nin'];
	$lname = $_POST['lname'];
	$fname = $_POST['fname'];
	$nin = filter_var(strip_tags(mysqli_real_escape_string($con,trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['nin']))) ), FILTER_SANITIZE_STRIPPED);
	$fname = filter_var(strip_tags(mysqli_real_escape_string($con,trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['fname']))) ), FILTER_SANITIZE_STRIPPED);
	$lname = filter_var(strip_tags(mysqli_real_escape_string($con,trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['lname']))) ), FILTER_SANITIZE_STRIPPED);
	$nok_name = filter_var(strip_tags(mysqli_real_escape_string($con,trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['nok_name']))) ), FILTER_SANITIZE_STRIPPED);
	$nok_phone = filter_var(strip_tags(mysqli_real_escape_string($con,trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['nok_phone']))) ), FILTER_SANITIZE_STRIPPED);

	if (intval($data['bal']) < 100) {
		die('304');
	}
	$curl = curl_init();
	curl_setopt_array($curl, array(
		CURLOPT_URL => 'https://app.eazymobile.ng/api/v2/seamless/nin/verify',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS =>'{
			"accessToken" : "1ac5a60d149c4f8ab1b8a5e0585a3b90",
			"kycType" : "NIN_VERIF",
			"ninNo" : "'.$nin.'",
			"merchantUrl" : "www.bsmconnekt.com",
			"firstName" : "'.$fname.'",
			"lastName" : "'.$lname.'"
		}',
		CURLOPT_HTTPHEADER => array(
			'Authorization: Bearer pk_live_dca52d96d9e4459194dd6b48c3bb9c94tla424',
			'Content-Type: application/json'
		),
	));

	$response = curl_exec($curl);
	curl_close($curl);



	$myfile = fopen("RESOURCE.txt", "a") or die("Unable to open file!");
	fwrite($myfile, '\r\n '.$current_date.': '.$response.'\r\n');
	fclose($myfile);
	
	$res = json_decode($response);
	if (isset($res->status)) {
		if ($res->status == true) {
			if (isset($res->response->data->verified) && $res->response->data->verified == 'SUCCESS') {
				$username = $data['username'];
				$reg_name = $data['name'];
				$new_name = $res->response->data->lastname.' '.$res->response->data->firstname;
				$new_name2 = $res->response->data->firstname.' '.$res->response->data->lastname;
				$oldBal = $data['bal'];
				$email = $data['email'];
				$username = $data['username'];
				$new_bal = strval(intval($oldBal) - 100);
				mysqli_query($con, "UPDATE users SET kyc_validate = 'true', kyc = '$response', bal = '$new_bal' WHERE username = '$username'");
				$ref = md5(time());
				mysqli_query($con, "INSERT INTO transactions (type, t_desc, ref, status, amount, oldBal, newBal, email, date_time, channel, username, amount_paid) VALUES ('DEDUCTION FOR KYC VERIFICATION', 'DEDUCTION FOR KYC VERIFICATION', '$ref', 'success', '100', '$oldBal', '$new_bal', '$email', '$current_date', 'KYC', '$username', '100')");
				if(strtoupper($new_name) != strtoupper($reg_name) && strtoupper($new_name2) != strtoupper($reg_name)){
					die('301');
				}
				mysqli_query($con, "INSERT INTO kyc (username, nin, firstname, lastname, birthdate, middlename, phone, gender, nationality, next_of_kin_phone, next_of_kin_name, image) VALUES ('$username', '{$res->response->data->nin}', '{$res->response->data->firstname}', '{$res->response->data->lastname}', '{$res->response->data->birthdate}', '{$res->response->data->middlename}', '{$res->response->data->phone}', '{$res->response->data->gender}', '{$res->response->data->nationality}', '$nok_phone', '$nok_name', '{$res->response->data->photo}')");
				
				die('200');
			}else{
				die('403');
			}
		}else{
			die('500');
		}
	}else{
		die('500');
	}

}


?>

<?php 
// Get primary WhatsApp number for widget
$whatsapp_numbers = [];
if (isset($config['whatsapp_num']) && !empty($config['whatsapp_num'])) {
    // Try decoding as JSON array first
    $decoded_whatsapp = json_decode($config['whatsapp_num'], true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_whatsapp)) {
        $whatsapp_numbers = $decoded_whatsapp;
    } elseif (is_string($config['whatsapp_num'])) {
         // Fallback: treat as a single number if not valid JSON
        $whatsapp_numbers = [$config['whatsapp_num']];
    }
}

// Get primary WhatsApp number for widget
$primary_whatsapp = !empty($whatsapp_numbers) ? $whatsapp_numbers[0] : '';
// Clean the number (remove non-digits)
$primary_whatsapp_clean = preg_replace('/[^0-9]/', '', $primary_whatsapp);
// Ensure it starts with country code, assuming 234 if it starts with 0 and is local length
if (substr($primary_whatsapp_clean, 0, 1) === '0' && (strlen($primary_whatsapp_clean) == 11 || strlen($primary_whatsapp_clean) == 10)) {
    $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 1);
} elseif (substr($primary_whatsapp_clean, 0, 3) === '234' && strlen($primary_whatsapp_clean) > 13) {
    // Handle cases like +2340... by removing the 0
    $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 3);
} elseif (substr($primary_whatsapp_clean, 0, 1) === '+') {
    $primary_whatsapp_clean = substr($primary_whatsapp_clean, 1); // Remove leading + if present
}
?>

<?php if (!empty($primary_whatsapp_clean)): ?>
<div class="whatsapp-widget">
    <!-- Floating chat button -->
    <button class="whatsapp-button" id="openWhatsappChat">
        <i class="bi bi-whatsapp"></i>
    </button>

    <!-- Chat popup -->
    <div class="whatsapp-popup" id="whatsappChatPopup">
        <div class="popup-header">
            <div class="popup-header-content">
                <img src="../../sharesublogo.png" alt="<?= htmlspecialchars($config['site_name']) ?>" width="40">
                <div>
                    <h6 class="mb-0"><?= htmlspecialchars($config['site_name']) ?> Support</h6>
                    <small class="text-muted">Usually replies within an hour</small>
                </div>
            </div>
            <button class="close-btn" id="closeWhatsappChat">×</button>
        </div>
        <div class="popup-body">
            <div class="message-container">
                <div class="received-message">
                    <p>Hello! How can we help you today?</p>
                    <small class="message-time">Now</small>
                </div>
            </div>
            <div class="message-input">
                <form id="whatsappMessageForm">
                    <input type="text" id="whatsappMessage" placeholder="Type a message..." required>
                    <button type="submit">
                        <i class="bi bi-send-fill"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- WhatsApp Widget Styles -->
<style>
    .whatsapp-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }

    .whatsapp-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #25D366;
        color: white;
        border: none;
        font-size: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        transition: all 0.3s;
    }

    .whatsapp-button:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 14px rgba(0, 0, 0, 0.2);
    }

    .whatsapp-popup {
        position: absolute;
        bottom: 80px;
        right: 0;
        width: 320px;
        background-color: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
        display: none;
    }

    .popup-header {
        background-color: #25D366;
        color: white;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .popup-header-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .popup-header img {
        border-radius: 50%;
        background-color: white;
        padding: 5px;
    }

    .close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
    }

    .popup-body {
        display: flex;
        flex-direction: column;
        height: 300px;
    }

    .message-container {
        flex-grow: 1;
        padding: 15px;
        overflow-y: auto;
    }

    .received-message,
    .sent-message {
        max-width: 80%;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 10px;
    }

    .received-message {
        background-color: #f0f0f0;
        align-self: flex-start;
    }

    .sent-message {
        background-color: #dcf8c6;
        align-self: flex-end;
        margin-left: auto;
    }

    .message-time {
        display: block;
        font-size: 10px;
        margin-top: 5px;
        opacity: 0.6;
    }

    .message-input {
        border-top: 1px solid #e0e0e0;
        padding: 10px;
    }

    .message-input form {
        display: flex;
    }

    .message-input input {
        flex-grow: 1;
        border: none;
        padding: 10px;
        outline: none;
    }

    .message-input button {
        background-color: transparent;
        border: none;
        color: #25D366;
        cursor: pointer;
        font-size: 20px;
    }
</style>

<!-- WhatsApp Widget Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const openBtn = document.getElementById('openWhatsappChat');
        const closeBtn = document.getElementById('closeWhatsappChat');
        const popup = document.getElementById('whatsappChatPopup');
        const messageForm = document.getElementById('whatsappMessageForm');
        const messageInput = document.getElementById('whatsappMessage');
        const messageContainer = document.querySelector('.message-container');
        
        // WhatsApp number from database
        const whatsappNumber = '<?= $primary_whatsapp_clean ?>';
        
        // Toggle chat popup
        openBtn.addEventListener('click', function() {
            popup.style.display = 'block';
        });
        
        closeBtn.addEventListener('click', function() {
            popup.style.display = 'none';
        });
        
        // Handle message sending
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (message) {
                // Add sent message to chat
                const sentMsg = document.createElement('div');
                sentMsg.className = 'sent-message';
                sentMsg.innerHTML = `
                    <p>${message}</p>
                    <small class="message-time">Just now</small>
                `;
                messageContainer.appendChild(sentMsg);
                
                // Clear input
                messageInput.value = '';
                
                // Scroll to bottom
                messageContainer.scrollTop = messageContainer.scrollHeight;
                
                // Open WhatsApp in new tab after a brief delay
                setTimeout(() => {
                    const encodedMessage = encodeURIComponent(message);
                    window.open(`https://wa.me/${whatsappNumber}?text=${encodedMessage}`, '_blank');
                    
                    // Hide popup after sending
                    popup.style.display = 'none';
                }, 500);
            }
        });
    });
</script>
<?php endif; ?>
