<?php
session_start();
include '../../conn.php';

header('Content-Type: application/json');

if (!isset($_SESSION['data']['username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

$username = $_SESSION['data']['username'];
$data = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM users WHERE username = '$username'"));

// Check if user has completed KYC
if (empty($data['BVN']) || empty($data['NIN'])) {
    echo json_encode(['success' => false, 'message' => 'KYC verification required. Please complete BVN and NIN verification first.']);
    exit();
}

// Get Payvessel configuration
$config = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM config"));
$payvesselConfig = json_decode($config['payvessel'], true);

if (!$payvesselConfig || !isset($payvesselConfig[0]) || !isset($payvesselConfig[1])) {
    echo json_encode(['success' => false, 'message' => 'Payvessel configuration not found']);
    exit();
}

$apiKey = $payvesselConfig[0];
$apiSecret = $payvesselConfig[1];

// Prepare request data for virtual account creation using correct Payvessel format
$requestData = [
    "email" => $data['email'],
    "name" => $data['name'],
    "phoneNumber" => $data['phone'],
    "bankcode" => ["999991", "120001"], // PalmPay and 9Payment Service Bank
    "account_type" => "STATIC",
    "businessid" => "6C990AF0A09D4030A18FF54DEE8D73A2", // Actual Business ID
    "bvn" => $data['BVN'],
    "nin" => $data['NIN']
];

// Debug: Log the request data
error_log("Payvessel API Request: " . json_encode($requestData));

// Make API call
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => "https://api.payvessel.com/pms/api/external/request/customerReservedAccount/",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => json_encode($requestData),
    CURLOPT_HTTPHEADER => [
        "api-key: " . $apiKey,
        "api-secret: Bearer " . $apiSecret,
        "Content-Type: application/json"
    ],
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$err = curl_error($curl);
curl_close($curl);

// Debug: Log the response
error_log("Payvessel API Response (HTTP $httpCode): " . $response);

if ($err) {
    echo json_encode(['success' => false, 'message' => 'Network error: ' . $err]);
    exit();
}

$responseData = json_decode($response, true);

if ($httpCode === 200 && isset($responseData['status']) && $responseData['status'] === true) {
    // Save account details to database
    $accountData = $responseData['banks'];
    $accountJson = json_encode($accountData);
    
    $updateQuery = "UPDATE users SET payvessel_accounts = '$accountJson' WHERE username = '$username'";
    if (mysqli_query($con, $updateQuery)) {
        echo json_encode([
            'success' => true, 
            'message' => 'Virtual accounts created successfully!',
            'data' => $accountData
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save account details: ' . mysqli_error($con)]);
    }
} else {
    $errorMessage = 'API call failed';
    if (isset($responseData['message'])) {
        $errorMessage = $responseData['message'];
    }
    echo json_encode(['success' => false, 'message' => $errorMessage]);
}

mysqli_close($con);
?> 