<?php
// Redirect to Payvessel KYC instead of old Monnify KYC
header('Location: payvessel_kyc.php');
exit;
?>
<?php
if (isset($_POST['updateKYC'])) {
    $kyc  = htmlspecialchars($_POST['kyc']);
    $type  = htmlspecialchars($_POST['type']);
    $type = ($type == 1) ? 'BVN' : (($type == 2) ? 'NIN' : '');

    if (strlen($kyc) != 11 || !is_numeric($kyc)) {
        echo "<script>
                Swal.fire('BVN/NIN must be 11 digits number!', '', 'error');
                setTimeout(function() {
                    window.location.href = 'kyc_update';      
                }, 400);
              </script>";
    } else {
        if (!empty($type)) {

            $kycUpdate = $reservedCreated = $failed = false;
            //$tokenKey = mysqli_fetch_assoc(mysqli_query($db, "SELECT * FROM monnify_key"));

            $apik = trim(json_decode($config['monnify'])[0]);
            $seck = trim(json_decode($config['monnify'])[2]);
            $apiCon = trim(json_decode($config['monnify'])[1]);
            $au = base64_encode("{$apik}:{$seck}");
            $userN = $_SESSION['data']['username'];
            $userE = $_SESSION['data']['email'];
            $userFN = $_SESSION['data']['name'];

            if ($apiCon) {
                $type = strtolower($type);

                $reserve = $_SESSION['data']['reserveBank'];
                $ref = null;

                if (!empty($reserve)) {
                    $wema = json_decode($reserve, true)[1];
                    $ref = $wema['reference'];
                    // $resQ = mysqli_query($con, "SELECT * FROM error WHERE det LIKE '%$acc%' LIMIT 1");
                    // $resultData = mysqli_fetch_assoc($resQ);
                    // $result = json_decode($resultData['det'], true);
                    // $ref = $result['responseBody']['accountReference'];
                }

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.monnify.com/api/v1/auth/login/',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Basic ' . $au,
                        'Content-Type: application/json'
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);

                if ($response) {
                    $tk = json_decode($response)->responseBody->accessToken;

                    if (!empty($ref)) {
                        $curl = curl_init();
                        curl_setopt_array($curl, array(
                            CURLOPT_URL => 'https://api.monnify.com/api/v1/bank-transfer/reserved-accounts/' . $ref . '/kyc-info',
                            CURLOPT_RETURNTRANSFER => true,
                            CURLOPT_ENCODING => '',
                            CURLOPT_MAXREDIRS => 10,
                            CURLOPT_TIMEOUT => 0,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                            CURLOPT_CUSTOMREQUEST => 'PUT',
                            CURLOPT_POSTFIELDS => json_encode([$type => $kyc]),
                            CURLOPT_HTTPHEADER => array(
                                "Content-Type: application/json",
                                "Authorization: Bearer " . $tk
                            ),
                        ));
                        $new_response = curl_exec($curl);
                        curl_close($curl);
                        if ($new_response) {
                            $current_date = date('Y-m-d H:i:s');
                            mysqli_query($con, "INSERT INTO error (name, det, timest) VALUES ('KYC UPDATE', '$new_response', '$current_date')");
                            $res = json_decode($new_response);
                            if ($res->requestSuccessful && $res->responseMessage == 'success') {
                                $mres = json_encode($res->responseBody);
                                mysqli_query($con, "UPDATE users SET $type = '$kyc', is_kyc='yes' WHERE username = '$userN'");
                                $kycUpdate = true;
                            }
                        }
                    } else {
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, "https://api.monnify.com/api/v2/bank-transfer/reserved-accounts");
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
                        curl_setopt($ch, CURLOPT_HEADER, FALSE);
                        curl_setopt($ch, CURLOPT_POST, TRUE);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
                            "accountName" => $userFN,
                            "accountReference" => $userE,
                            "currencyCode" => "NGN",
                            "contractCode" => $apiCon,
                            "customerName" => $userFN,
                            "customerEmail" => $userE,
                            "getAllAvailableBanks" => false,
                            $type => $kyc,
                            "preferredBanks" => ["50515", "035", "232"]
                        ]));

                        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                            "Content-Type: application/json",
                            "Authorization: Bearer " . $tk
                        ));
                        $response = curl_exec($ch);
                        curl_close($ch);

                        $current_date = date('Y-m-d H:i:s');
                        mysqli_query($con, "INSERT INTO error (name, det, timest) VALUES ('RESERVED ACCOUNT', '$response', '$current_date')");
                        $response = json_decode($response, true);
                        if ($response['requestSuccessful']) {
                            $accounts = $response['responseBody']['accounts'];
                            $accountReference = $response['responseBody']['accountReference'];
                            $selectedData = [];
                            foreach ($accounts as $account) {
                                $selectedData[] = [
                                    'bankCode' => $account['bankCode'],
                                    'bankName' => $account['bankName'],
                                    'accountNumber' => $account['accountNumber'],
                                    'accountName' => $account['accountName'],
                                    'reference' => $accountReference
                                ];
                            }
                            $genAccDet = json_encode($selectedData);
                            $resData =   json_encode($response['responseBody']);
                            mysqli_query($con, "UPDATE users SET reserveBank = '$genAccDet', reserveData = '$resData' , $type = '$kyc' WHERE  username = '$userN'");
                            $reservedCreated = true;
                        } else {

                            $msg = $response['responseMessage'];
                            $failed = true;
                        }
                    }
                }
            }
        }

        if ($kycUpdate) {
            echo "<script>
                Swal.fire('BVN/NIN Updated Successfully', '', 'success').then(function() {
                    window.location.href = 'index';      
                });          
            </script>";
        } else if ($reservedCreated) {
            echo "<script>
                Swal.fire('Reserved Account Created successfully', 'congrats!', 'success').then(function() {
                    window.location.href = 'index';      
                });
            </script>";
        } else if ($failed) {
            echo "<script>
                Swal.fire('$msg', 'Please try again', 'error').then(function() {
                    window.location.href = 'kyc_update';      
                });
            </script>";
        } else {
            echo "<script>
                Swal.fire('INVALID BVN/NIN” please enter a VALID BVN OR NIN', 'Please try again', 'error').then(function() {
                    window.location.href = 'kyc_update';      
                });
            </script>";
        }
    }
}
?>
<div class="container-fluid">
    <div class="row">

        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title"> <?= empty($_SESSION['data']['reserveBank']) ? 'GENERATE ACCOUNT NUMBER' : 'UPDATE ACCOUNT KYC'; ?></h4>
                    </p>
                    <div class="table-responsive">

                        <div class="form-group">
                            <div class="alert alert-danger" style="display:none; font-size:15px;" id="error"></div>
                        </div>

                        <form action="" method="post">
                            <div class="modal-body">

                                <div class="form-group">
                                    <select name="type" class="form-control" required>
                                        <option selected>Select Type</option>
                                        <option value="1">BVN</option>
                                        <option value="2">NIN</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="password-1">Digits</label>
                                    <input type="number" class="form-control" name="kyc" placeholder="Enter BVN/NIN Digits" required>
                                </div>


                                <div class="form-group">
                                    <label for="password-1">Full Name</label>
                                    <input type="text" class="form-control" name="name" value="<?php echo $_SESSION['data']['name'] ?>" disabled>
                                </div>

                                <hr>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary" name="updateKYC">
                                        <?php echo empty($_SESSION['data']['reserveBank']) ? 'GENERATE ACCOUNT NUMBER' : 'UPDATE ACCOUNT KYC'; ?>
                                    </button>
                                </div>



                            </div>
                        </form>







                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<?php
include 'footer.php';
?>