<?php 
$actual_link = $_SERVER['REMOTE_ADDR'];
//if($actual_link != '198.187.31.83') die('Invalid REQUEST');
include "../../conn.php";

if ($_SERVER["REQUEST_METHOD"] == "GET") {
	die(json_encode(array('error' => true, 'desc' => 'Unsupported Method')));
}
$headers = apache_request_headers();
if (!isset($headers['Authorization']) || empty($headers['Authorization'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
}

if (!isset($_POST['card_number']) || empty($_POST['card_number'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid card_number')));
}
if (!isset($_POST['decoder_name']) || empty($_POST['decoder_name'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid decoder_name')));
}

$card_number = mysqli_real_escape_string($con, $_POST['card_number']);
$service_id = strtolower(mysqli_real_escape_string($con, $_POST['decoder_name']));
$req_token = mysqli_real_escape_string($con, $headers['Authorization']);

// if (!str_contains($req_token, 'Token')) {
// 	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
// }

// if (!isset(explode(' ', $req_token)[1]) || empty(explode(' ', $req_token)[1])) {
// 	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
// }

// $token = explode(' ', $req_token)[1];


$netsToken = mysqli_query($con, "SELECT * FROM apikey WHERE platform='vtpass' AND types='airtime' ");
$tokenKey = mysqli_fetch_assoc($netsToken);
if (!$tokenKey) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid apikey')));
}
$apiLink = "https://vtpass.com/api/merchant-verify";//$tokenKey['apiLink'];
$apiKey = $tokenKey['apiKey'];
$secretkey = $tokenKey['secretkey'];

$curl = curl_init();
$payload = ['api-key: ' . $apiKey, 'secret-key: ' . $secretkey];
curl_setopt_array($curl, array(
	CURLOPT_URL => $apiLink,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_ENCODING => '',
	CURLOPT_MAXREDIRS => 10,
	CURLOPT_TIMEOUT => 0,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	CURLOPT_CUSTOMREQUEST => 'POST',
	CURLOPT_POSTFIELDS => array('billersCode' => $card_number,'serviceID' => $service_id),
	CURLOPT_HTTPHEADER => $payload,
));

$response = curl_exec($curl);

curl_close($curl);

$resp = json_decode($response);


if ($resp->code == '000') {
	if (isset($resp->content->error)) {
		die(json_encode(array('error' => true, 'status' => 400, 'desc' => $resp->content->error)));
	}else{
		die(json_encode(array('error' => false, 'status' => 200, 'name' => $resp->content->Customer_Name)));
	}
}else{
	die(json_encode(array('error' => true, 'status' => 407, 'desc' => $resp->response_description)));
}



?>