<?php
include 'header.php';
$username = $data['username'];
$email = $data['email'];
$all_user_types = [];
$uQ = mysqli_query($con, "SELECT * FROM user_types");
while ($uT = mysqli_fetch_assoc($uQ)) {
  array_push($all_user_types, $uT);
}

$trs = mysqli_num_rows(mysqli_query($con, "SELECT id FROM transactions WHERE username = '$username' OR email = '$email'"));

?>
<!--<?php if ($data['status'] == 'verified' && isset($_SESSION['config']['msg']) && !empty(trim($_SESSION['config']['msg']))) : ?>-->
<!--<script type="text/javascript">-->
<!--  Swal.fire('', "<?= $_SESSION['config']['msg'] ?>")-->
<!--</script>-->
<!--<?php endif ?>-->


<?php if ($data['status'] == 'verified' && isset($_SESSION['config']['pop_info']) && !empty(trim($_SESSION['config']['pop_info']))) : ?>
  <script>
    Swal.fire({
      title: 'Notification!',
      text: "<?= $_SESSION['config']['pop_info'] ?>",
      //imageUrl: "../<?= $_SESSION['config']['pop_image'] ?>",
      //imageWidth: 400,
      //imageHeight: 200,
    })
  </script>
<?php endif ?>


<div class="container-fluid" style="margin-top: 50px;">

  <div class="header-body">
    <div class="row align-items-center py-4">
      <div class="col-lg-6 col-8">
        <h6 class="h2 text-white d-inline-block mb-0"><span id="greeting"></span>, <?= explode(' ', $data['name'])[1] ?>. <span id="timerSpan"></span></h6>

      </div>

    </div>

    <!-- Card stats -->
    <div class="row">
      <div class="col-xl-6 col-md-6">
        <div class="card card-stats">
          <!-- Card body -->
          <div class="card-body">
            <div class="row">
              <div class="col">
                <h5 class="card-title text-uppercase text-muted mb-0">Wallet Balance</h5>
                <span class="h2 font-weight-bold mb-0">₦<?= parseAmt($data['bal']) ?></span>
              </div>
              <div class="col-auto">
                <div class="icon icon-shape bg-gradient-green text-white rounded-circle shadow">
                  <i class="ni ni-money-coins"></i>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="col-xl-6 col-md-6">
        <div class="card card-stats">
          <!-- Card body -->
          <div class="card-body">
            <div class="row">
              <div class="col">
                <h5 class="card-title text-uppercase text-muted mb-0">Commision</h5>
                <span class="h2 font-weight-bold mb-0">
                  <?php
                  $tRef = json_decode($data['refs']);
                  $tCRef = json_decode($data['compRef']);
                  $bns = count(array_diff($tRef, $tCRef)) * intval($config['refBonus']);
                  echo $bns;
                  ?>
                </span>
              </div>
              <div class="col-auto">
                <div class="icon icon-shape bg-gradient-red text-white rounded-circle shadow">
                  <i class="ni ni-active-40"></i>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <?php if (isset($config['msg']) && !empty($config['msg'])) : ?>
        <marquee width="100%" direction="left" height="40px" style='color: white'> <?= $config['msg'] ?> </marquee>
      <?php endif ?>
    </div>
  </div>
</div>
</div>

<!-- Page content -->
<div class="container-fluid mt--8">
  <br>
  <?php if ($config['extra'] != '') : ?>
    <div class="card">
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-4">

            <!--<div class="card-header">-->
            <!--  <h5>ADVERTORIALS!</h5>-->
            <!--</div>-->

            <?= str_replace('#name', json_decode($config['user_type_names'])[1], $config['extra']) ?>

          </div>
          <div class="col-md-6"><img style="max-height: 100%; max-width: 100%; border-radius: 10px" src="../<?= $config['notUrl'] ?>" alt=""></div>
        </div>
      </div>
    </div>
    <br>
  <?php endif ?>

  <div class="card">

    <div onclick="textCopy()" class="card-body text-dark">
      <h3 class="card-text mb-0" style="color: black;">REFER AND EARN</h3>
      <p style="color: gray; font-weight: bold;"><b>Did you know you can make up to ₦5000 daily if you refer people to use this platform? Try it today!</b></p>
      http://<?= $config['site_link'] ?>/web/sign_up?referrer=<?= $data['username'] ?> <sup><span class="badge badge-primary " style="color: orange; font-size: 10px">TAP ON ME TO COPY</span></sup>
    </div>
  </div>

  <center class="mb-3">
    <a href="<?= $config['apkLink'] ?>" class="btn btn-primary btn-lg">DOWNLOAD OUR APP</a>
    <br>
    <!-- <div class="mt-2">
        Account KYC Status: <?php
                            if ($data['kyc_validate'] == 'true') {
                            ?>
          <strong><i>VERIFIED</i></strong>
          <?php
                            } else {
          ?>
          <strong><i>UNVERIFIED</i></strong>

          <p class="mt-4"><a class="btn btn-primary" href="payvessel_kyc.php">VERIFY</a></p>
          <?php
                            }

          ?>
      </div> -->

    <!-- <div>
        <?php
        if ($data['kyc_validate'] != 'true') {
        ?>
          <h3 style='color: orange'>Dear customer, kindly know that your account is UNVERIFIED, and it is LIMITED to a transaction amount of ₦<?= $config['day_spent_amount'] ?> per day. Kindly please click on the VERIFY button to verify your account.</h3>
          <?php
        }
          ?>
      </div> -->
  </center>
  <div class="row">

    <div class="col">

      <button type="button" class="btn btn-primary">
        <span>CURRENT PLAN: </span>
        <span class="badge badge-floating badge-danger border-white"><?php
                                                                      foreach ($all_user_types as $value) {

                                                                        if (str_contains($data['type'], $value['id'])) {
                                                                          echo $value['name'];
                                                                        }
                                                                      }
                                                                      ?></span>
      </button>
    </div>
    <?php if ($data['type'] == 'user1') : ?>
      <div class="col-md-6 mt-2" onclick="Swal.fire({
        title: `Are you sure?`,
        html: `You are upgrading to <?= json_decode($config['user_type_names'])[1] ?> plan. <br> <b>Note:</b> You will be charged a sum of ₦<?= $config['upgradingCost'] ?>.`,
        confirmButtonText: 'Yes, Continue',
        showCancelButton: true,
        cancelButtonText: `No, I'm not ready!`

      }).then((res)=>{
        if (res.isConfirmed) {
          window.location.href = 'upgrade_plan'
        }
      })">
        <button class="btn btn-primary"><?= str_replace('#name', json_decode($config['user_type_names'])[1], $config['upgradeCaption']) ?></button>
        <!-- <button class="btn btn-primary">UPGRADE TO LEVEL 2</button> -->
      </div>
    <?php endif ?>
  </div>
  <br>
  <div class="alert alert-primary alert-dismissible fade show" role="alert">

    <span class="alert-text text-white"><strong style="color: orange;"> **NEW** </strong> Own a VTU retailer website and retail all our services; Such as DATA, Recharge cards printing, AIRTIME and BILLS Payment.!</span>
    <a href="https://api.whatsapp.com/send?phone=234<?= substr(json_decode($config['whatsapp_num'])[0], 1); ?>&text=Hi,%20I%20want%20to%20get%20a%20VTU/data%20website.%20" class="btn btn-warning mt-2" target="_blank">
      Click Here
    </a>
  </div>
  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-body">
        
          <div class="row">
            <div class="col">

              <div class="nav-wrapper">
                <ul class="nav nav-pills nav-fill flex-column flex-md-row" id="tabs-icons-text" role="tablist">
                  <?php 
                  // Check if Monnify is enabled
                  $monnify_enabled = !isset($config['monnify_enabled']) || $config['monnify_enabled'] != 'disabled';
                  
                  // Check if user has Payvessel accounts
                  $payvessel_accounts = !empty($data['payvessel_accounts']) ? json_decode($data['payvessel_accounts'], true) : null;
                  $payvessel_kyc_done = !empty($data['BVN']) || !empty($data['NIN']);
                  ?>
                  
                  <?php if ($data['reserveBank'] != '' && $monnify_enabled): ?>
                    <!-- Monnify Tabs (Show only when enabled) -->
                    <li class="nav-item">
                      <a class="nav-link mb-sm-3 mb-md-0 active" id="tabs-icons-text-1-tab" data-toggle="tab" href="#tabs-icons-text-1" role="tab" aria-controls="tabs-icons-text-1" aria-selected="true"><i class="ni ni-button-play mr-2"></i>WEMA BANK</a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-2-tab" data-toggle="tab" href="#tabs-icons-text-2" role="tab" aria-controls="tabs-icons-text-2" aria-selected="false"><i class="ni ni-button-play mr-2"></i>MONIEPOINT BANK</a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link mb-sm-3 mb-md-0" id="tabs-icons-text-3-tab" data-toggle="tab" href="#tabs-icons-text-3" role="tab" aria-controls="tabs-icons-text-3" aria-selected="false"><i class="ni ni-button-play mr-2"></i>STERLING BANK</a>
                    </li>
                  <?php endif ?>
                  
                  <?php if (!$monnify_enabled || $payvessel_accounts): ?>
                    <!-- Payvessel Tab (Show when Monnify is disabled or if user has Payvessel accounts) -->
                    <li class="nav-item">
                      <a class="nav-link mb-sm-3 mb-md-0 <?= (!$monnify_enabled || !$payvessel_accounts) ? 'active' : '' ?>" id="tabs-icons-text-4-tab" data-toggle="tab" href="#tabs-icons-text-4" role="tab" aria-controls="tabs-icons-text-4" aria-selected="<?= (!$monnify_enabled || !$payvessel_accounts) ? 'true' : 'false' ?>"><i class="fa fa-university mr-2"></i>PAYVESSEL</a>
                    </li>
                  <?php endif ?>
                </ul>
              </div>


              <div class="card shadow">

                <div class="tab-content" id="myTabContent">

                  <?php 
                  // Check if Monnify is enabled
                  $monnify_enabled = !isset($config['monnify_enabled']) || $config['monnify_enabled'] != 'disabled';
                  
                  // Check if user has Payvessel accounts
                  $payvessel_accounts = !empty($data['payvessel_accounts']) ? json_decode($data['payvessel_accounts'], true) : null;
                  $payvessel_kyc_done = !empty($data['BVN']) || !empty($data['NIN']);
                  ?>

                  <?php if ($data['reserveBank'] != '' && $monnify_enabled) : ?>
                    <!-- Monnify Accounts (Show only when enabled) -->
                    <div class="tab-pane fade show active" id="tabs-icons-text-1" role="tabpanel" aria-labelledby="tabs-icons-text-1-tab">
                      <div class="card bg-gradient-warning card-img-holder text-white">
                        <div class="card-body">

                          <img class="mb-3" width="40%" src="https://cdn.punchng.com/wp-content/uploads/2016/12/********/Wema-Bank-Logo.png" class="card-img-absolute" alt="circle-image" />
                          <br>
                          <?php foreach (json_decode($data['reserveBank']) as $value) : ?>
                            <?php if ($value->bankName == 'Wema bank') : ?>
                              <h1 class="mb-4" style="color: white;"><?= $value->accountNumber ?></h1>
                              <h4 class="mb-3" style="color: white;"><?= $config['site_name'] ?>-<?= $value->accountName ?></h4>
                            <?php endif ?>
                          <?php endforeach ?>

                          <p class="card-text" style="color: white;">Charge: 1.2%. <br> For any amount you transfer to this account, 1.2% will be debited, and your wallet funded INSTANTLY</p>
                        </div>
                      </div>
                    </div>

                    <div class="tab-pane fade" id="tabs-icons-text-2" role="tabpanel" aria-labelledby="tabs-icons-text-2-tab">
                      <div class="card bg-gradient-info card-img-holder text-white">
                        <div class="card-body">

                          <img class="mb-3" width="60%" src="https://nigerialogos.com/logos/moniepoint/moniepoint.svg" class="card-img-absolute" alt="circle-image" />

                          <?php foreach (json_decode($data['reserveBank']) as $value) : ?>

                            <?php if ($value->bankName == 'Moniepoint Microfinance Bank') : ?>
                              <h1 class="mb-4" style="color: white;"><?= $value->accountNumber ?></h1>
                              <h4 class="mb-3" style="color: white;"><?= $config['site_name'] ?>-<?= $value->accountName ?></h4>
                            <?php endif ?>
                          <?php endforeach ?>


                          <p class="card-text" style="color: white;">Charge: 1.2%. <br> For any amount you transfer to this account, 1.2% will be debited, and your wallet funded INSTANTLY</p>
                        </div>
                      </div>
                    </div>

                    <div class="tab-pane fade" id="tabs-icons-text-3" role="tabpanel" aria-labelledby="tabs-icons-text-3-tab">
                      <div class="card bg-gradient-danger card-img-holder text-white">
                        <div class="card-body">

                          <img width="30%" src="https://upload.wikimedia.org/wikipedia/commons/0/07/Sterling_Bank_Logo_Straight.png" class="card-img-absolute" alt="circle-image" />
                          <h5 class="font-weight-normal mb-3" style="color: white;">Sterling Bank
                          </h5>
                          <?php foreach (json_decode($data['reserveBank']) as $value) : ?>
                            <?php if ($value->bankName == 'Sterling bank') : ?>
                              <h1 class="mb-4" style="color: white;"><?= $value->accountNumber ?></h1>
                              <h4 class="mb-3" style="color: white;"><?= $config['site_name'] ?>-<?= $value->accountName ?></h4>
                            <?php endif ?>
                          <?php endforeach ?>

                          <p class="card-text" style="color: white;">Charge: 1.2%. <br> For any amount you transfer to this account, 1.2% will be debited, and your wallet funded INSTANTLY</p>


                        </div>
                      </div>
                    </div>
                  <?php endif ?>

                  <?php if (!$monnify_enabled || $payvessel_accounts): ?>
                    <!-- Payvessel Accounts (Show when Monnify is disabled or if user has Payvessel accounts) -->
                    <div class="tab-pane fade show <?= (!$monnify_enabled || !$payvessel_accounts) ? 'active' : '' ?>" id="tabs-icons-text-4" role="tabpanel" aria-labelledby="tabs-icons-text-4-tab">
                      <?php if ($payvessel_accounts): ?>
                        <?php foreach ($payvessel_accounts as $account): ?>
                          <div class="card bg-gradient-success card-img-holder text-white">
                            <div class="card-body">
                              <div class="d-flex align-items-center mb-3">
                                <?php if (strpos(strtolower($account['bankName']), 'palm') !== false): ?>
                                  <img src="../assets/img/palmpay.png" class="mr-2" alt="PalmPay" style="height: 40px; width: auto;">
                                <?php elseif (strpos(strtolower($account['bankName']), '9payment') !== false): ?>
                                  <img src="../assets/img/9PSB.png" class="mr-2" alt="9Payment Service Bank" style="height: 40px; width: auto;">
                                <?php else: ?>
                                  <img src="https://via.placeholder.com/40x40/ffffff/6c757d?text=B" class="mr-2" alt="Bank">
                                <?php endif; ?>
                                <h5 class="font-weight-normal mb-0" style="color: white;"><?= htmlspecialchars($account['bankName']) ?></h5>
                              </div>
                              
                              <h1 class="mb-4" style="color: white;"><?= htmlspecialchars($account['accountNumber']) ?></h1>
                              <h4 class="mb-3" style="color: white;"><?= htmlspecialchars($account['accountName']) ?></h4>

                              <p class="card-text" style="color: white;">Charge: 1.0% of transfer amount. <br> For any amount you transfer to this account, 1.0% will be debited, and your wallet funded INSTANTLY</p>
                            </div>
                          </div>
                        <?php endforeach ?>
                      <?php elseif ($payvessel_kyc_done): ?>
                        <div class="card bg-gradient-success card-img-holder text-white">
                          <div class="card-body text-center">
                            <h5 class="font-weight-normal mb-3" style="color: white;">Payvessel Virtual Accounts</h5>
                            <p class="card-text" style="color: white;">You haven't generated virtual accounts yet.</p>
                            <a href="payvessel_virtual.php" class="btn btn-light">
                              <i class="fa fa-university"></i> Generate Virtual Accounts
                            </a>
                          </div>
                        </div>
                      <?php else: ?>
                        <div class="card bg-gradient-success card-img-holder text-white">
                          <div class="card-body text-center">
                            <h5 class="font-weight-normal mb-3" style="color: white;">Payvessel Virtual Accounts</h5>
                            <p class="card-text" style="color: white;">Complete KYC verification to generate virtual accounts.</p>
                            <a href="payvessel_kyc.php" class="btn btn-light">
                              <i class="fa fa-user-check"></i> Complete KYC
                            </a>
                          </div>
                        </div>
                      <?php endif ?>
                    </div>
                  <?php endif ?>
                </div>

              </div>




            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col">

      <div class="col my-2 d-none d-md-block">
        <a href="transactions" class="card bg-primary">
          <button type="button" class="btn btn-link btn-sm card-body text-white" style="font-size: 18px;">Transactions History</button>
        </a>
        <a href="transactions" class="card bg-primary">
          <button type="button" class="btn btn-link btn-sm card-body text-white" style="font-size: 18px;">Airtime Top Up</button>
        </a>
        <a href="transactions" class="card bg-primary">
          <button type="button" class="btn btn-link btn-sm card-body text-white" style="font-size: 18px;">DATA Top Up</button>
        </a>
        <a href="transactions" class="card bg-primary">
          <button type="button" class="btn btn-link btn-sm card-body text-white" style="font-size: 18px;">Wallet Funding (ATM)</button>
        </a>
        <a href="transactions" class="card bg-primary">
          <button type="button" class="btn btn-link btn-sm card-body text-white" style="font-size: 18px;">CABLE SUBSCRIPTION</button>
        </a>
        <a href="transactions" class="card bg-primary">
          <button type="button" class="btn btn-link btn-sm card-body text-white" style="font-size: 18px;">BILL PAYMENT</button>
        </a>
      </div>

    </div>
  </div>

  <div class="row">

    <div class="col my-2">
      <a href="data_purchase" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="assets/images/data.jpg" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Data <br> Top-Up</h5>
        </div>
      </a>
    </div>

    <div class="col my-2">
      <a href="airtime" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="assets/images/airtime.svg" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Airtime <br> Top-Up</h5>
        </div>
      </a>
    </div>

    <div class="col my-2">
      <a href="bill" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="assets/images/utility.jpg" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Electricity <br> Payment</h5>
        </div>
      </a>
    </div>
    <div class="col my-2">
      <a href="cable" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="assets/images/cable.jpg" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Cable TV <br> Subscription</h5>
        </div>
      </a>
    </div>


  </div>
  <div class="row">
    <div class="col my-2">
      <a href="auto" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="assets/images/money.jpg" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Wallet <br> Funding</h5>
        </div>
      </a>
    </div>
    <div class="col my-2">
      <a href="referral" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="https://www.pngitem.com/pimgs/m/341-3414895_referral-icon-png-transparent-png.png" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Referral <br> Page</h5>
        </div>
      </a>
    </div>
    <div class="col my-2">
      <a href="settings" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="assets/images/settings.png" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Profile <br> Update</h5>
        </div>
      </a>
    </div>



    <!--<div class="col my-2">
    <a onclick="Swal.fire('You want to log out?','','question').then((res)=>{if(res.isConfirmed){
      window.location.replace('logout');
    }})" class="card text-center">
    <div class="card-body text-secondary">
      <div class="img mb-2"> <img src="assets/images/logout.png" width="70" class="rounded-circle"> </div>
      <h5 class="mb-0">Log-Out</h5>
    </div>
  </a>
</div>
</div>-->

    <div class="col my-2">
      <a href="data_card" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="assets/images/airtimess.png" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Data Card<br>Print</h5>
        </div>
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col my-2">
      <a href="a2c" class="card text-center">
        <div class="card-body text-secondary">
          <div class="img mb-2"> <img src="https://img.utdstc.com/icon/bf9/1ff/bf91ffae0173aeb8715f832bdf59337a3786751d1e9f6732d32a5df2866fc3e3:200" width="70" class="rounded-circle"> </div>
          <h5 class="mb-0">Airtime2Cash</h5>
        </div>
      </a>
    </div>
         <div class="col my-2">
       <a href="payvessel_kyc.php" class="card text-center">
         <div class="card-body text-secondary">
           <div class="img mb-2"> <img src="https://media.istockphoto.com/photos/person-placing-red-percentage-block-over-vat-picture-id1322047961?k=20&m=1322047961&s=612x612&w=0&h=3QEArqnd1OSjd4h1aoRPMfYPwZ29le1JhtbxqGF7wjE=" width="100" class="rounded-circle"> </div>
           <h5 class="mb-0">KYC</h5>
         </div>
       </a>
     </div>

  </div>


  <?php if ($config['ad_config'] == 'true') : ?>
    <?php
    $sqlD = "SELECT * FROM Advert ORDER BY RAND() LIMIT 3;";
    $resultD = $con->query($sqlD);
    ?>
    <?php if ($resultD->num_rows > 0) : ?>
      <?php while ($rowD = $resultD->fetch_assoc()) : ?>
        <div class="card mb-3 z-index-2 h-100">
          <h3 class="card-header card-title"><?= ucwords($rowD['titles']) ?></h3>
          <img src="<?= $rowD['images'] ?>" class="card-img-top" alt="...">
          <div class="card-body">
            <h2 class="card-title d-inline p-2 ">Price: <div class="text-success d-inline p-2 ">₦<?= number_format($rowD['price']) ?></div>
            </h2>
            <a href="https://wa.me/234<?= substr(json_decode($config['whatsapp_num'])[0], 1); ?>?text=Hi%2C%20Admin%2C%20I'm%20from%20<?= $config['site_link']; ?>%20I%20want%20to%20buy%20your%20product....%0A%0AProduct%20Name%3A%20<?= ucwords($rowD['titles']) ?>%0A%0AProduct%20Price%3A%20%E2%82%A6<?= number_format($rowD['price']) ?>" class="btn btn-primary text-center "><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cart" viewBox="0 0 16 16">
                <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z" />
              </svg> Buy Now!</a>
          </div>
        </div>
      <?php endwhile; ?>
    <?php else : ?>
      <p>No products found.</p>
    <?php endif; ?>
  <?php endif; ?>


  <div class="col-lg-12 mb-lg-0 mb-4">
    <div class="card z-index-2 h-100">
      <div class="card-header pb-0 pt-3 bg-transparent">
        <h6 class="text-capitalize">MESSAGE</h6>

      </div>
      <div class="card-body p-3">
        <blockquote class="blockquote blockquote-primary">
          <h5> <?= $config['msg'] ?> </h5>
        </blockquote>
        <!-- <div class="chart">
            <canvas id="chart-line" class="chart-canvas" height="300"></canvas>
          </div> -->
      </div>
    </div>
  </div>

  <div class="col-md-6 mt-3">
    <div class="card card-dark bg-info-gradient">
      <div class="card-body bubble-shadow">
        <h3>Don't get bored!</h3>
        <h5 class="op-9" id="typ"> </h5>

      </div>
    </div>
  </div>

  <?php
  include 'footer.php';


  ?>
  <script type="text/javascript">
    const today = new Date()
    const now = today.getHours()
    const greeting = document.getElementById("greeting")
    const msg = document.getElementById("timerSpan")
    let message = 'Good'
    let msgC
    console.log(now)
    // function theTimer() {
    //   const d = new Date();
    //   console.log(d.toLocaleTimeString())
    //   document.getElementById("timerSpan").innerHTML = d.toLocaleTimeString();
    // }
    // setInterval(theTimer, 1000);
    if (now < 12 && now > 5) {
      message = `${message} Morning`;
      msgC = ` 😋`
    } else if (now < 19 && now > 11) {
      message = `${message} Afternoon`
      msgC = `It is working Time! 💻`
    } else {
      message = `${message} Evening`
      msgC = `It is Sleepy time! 😴`
    }

    greeting.innerText = message;
    msg.innerText = msgC;

    function textCopy() {
      var text = "http://<?= $config['site_link'] ?>/web/sign_up?referrer=<?= $data['username'] ?>";
      var textArea = document.createElement("textarea");
      textArea.value = text;

      // Avoid scrolling to bottom
      textArea.style.top = "0";
      textArea.style.left = "0";
      textArea.style.position = "fixed";

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        var successful = document.execCommand('copy');
        var msg = successful ? 'Link Copied' : 'Unable to copy';
        Swal.fire(msg);
        console.log('Fallback: Copying text command was ' + msg);
      } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
      }

      document.body.removeChild(textArea);
    }

    function copyTextToClipboard(text) {
      if (!navigator.clipboard) {
        fallbackCopyTextToClipboard(text);
        return;
      }
      navigator.clipboard.writeText(text).then(function() {
        console.log('Async: Copying to clipboard was successful!');
      }, function(err) {
        console.error('Async: Could not copy text: ', err);
      });
    }
  </script>
  <script src="js/typed.js"></script>
  <script>
    $(function() {
      $("#typ").typed({
        strings: ["“Success is not final; failure is not fatal: it is the courage to continue that counts.” — Winston Churchill^3000", "“Play by the rules, but be ferocious.” — Phil Knight^3000", "“Business opportunities are like buses, there’s always another one coming.” — Richard Branson^3000", "“Every problem is a gift—without problems we would not grow.” — Anthony Robbins^3000", "“You only have to do a few things right in your life so long as you don’t do too many things wrong.” — Warren Buffett^3000", "“Success usually comes to those who are too busy to be looking for it.” — Henry David Thoreau^3000", "“And the day came when the risk to remain tight in a bud was more painful than the risk it took to blossom.” — Anaïs Nin^3000", "“There’s no shortage of remarkable ideas, what’s missing is the will to execute them.” — Seth Godin^3000", "“I owe my success to having listened respectfully to the very best advice, and then going away and doing the exact opposite.” — G.K. Chesterton^3000", "“I don’t know the word ‘quit.’ Either I never did, or I have abolished it.” — Susan Butcher^3000", "“Far and away the best prize that life offers is the chance to work hard at work worth doing.” — Theodore Roosevelt^3000", "“If you really look closely, most overnight successes took a long time.” — Steve Jobs^3000", "Almost everything worthwhile carries with it some sort of risk, whether it’s starting a new business, whether it’s leaving home, whether it’s getting married, or whether it’s flying into space.” — Chris Hadfield^3000", "“Even if you are on the right track, you’ll get run over if you just sit there.” — Will Rodgers^3000", "“The real test is not whether you avoid this failure, because you won’t. It’s whether you let it harden or shame you into inaction, or whether you learn from it; whether you choose to persevere.” — Barack Obama^3000", "“Forget past mistakes. Forget failures. Forget everything except what you’re going to do now and do it.” — William Durant^3000", "“Imagination is everything. It is the preview of life’s coming attractions.” — Albert Einstein^3000", "“Character cannot be developed in ease and quiet. Only through experience of trial and suffering can the soul be strengthened, ambition inspired and success achieved.” — Helen Keller^3000", "“The first one gets the oyster, the second gets the shell.” — Andrew Carnegie^3000", "“The way to get started is to quit talking and begin doing.” — Walt Disney^3000", "“Whether you think you can or whether you think you can’t, you’re right!” — Henry Ford^3000", "“There are no secrets to success. It is the result of preparation, hard work and learning from failure.” — Colin Powell^3000", "“Success is not the key to happiness. Happiness is the key to success. If you love what you are doing, you will be successful.” — Albert Schweitzer^3000", "“Success is often achieved by those who don’t know that failure is inevitable.” — Coco Chanel^3000", "“Many of life’s failures are people who did not realize how close they were to success when they gave up.” — Thomas Edison^3000", "“I feel that luck is preparation meeting opportunity.” — Oprah Winfrey^3000", "“I never dreamed about success. I worked for it.” — Estée Lauder^3000", "“Yesterday’s home runs don’t win today’s games.” — Babe Ruth^3000", " “The only place where success comes before work is in the dictionary.” — Vidal Sassoon^3000", "“The only way around is through.” — Robert Frost^10000"],
        typeSpeed: 5,
        loop: true,
        startDelay: 0,
        showCursor: true
      });
    });
  </script>