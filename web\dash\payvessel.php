<?php
include 'header.php';

// Check if Payvessel is configured
$payvessel_config = json_decode($config['payvessel'], true);
$payvessel_enabled = !empty($payvessel_config) && is_array($payvessel_config) && count($payvessel_config) >= 2 && !empty($payvessel_config[0]) && !empty($payvessel_config[1]);
?>

<div class="container-fluid mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Payvessel Card Payment</h3>
          <p class="card-description">Fund your wallet using Payvessel payment gateway</p>
        </div>
        <div class="card-body">
          
          <?php if (!$payvessel_enabled): ?>
            <div class="alert alert-warning">
              <i class="fa fa-exclamation-triangle"></i>
              <strong>Payvessel is not configured!</strong> Please contact the administrator to set up Payvessel payment gateway.
            </div>
          <?php else: ?>
            
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="amount">Amount (₦)</label>
                  <input type="number" id="amount" class="form-control" placeholder="Enter amount" min="100" max="5000">
                  <small class="form-text text-muted">Minimum: ₦100, Maximum: ₦5000</small>
                </div>
                
                <div class="form-group">
                  <label>Charge</label>
                  <div class="input-group">
                    <span class="input-group-text">₦</span>
                    <input type="text" id="charge" class="form-control" readonly>
                  </div>
                  <small class="form-text text-muted">Charge: 1.5%</small>
                </div>
                
                <div class="form-group">
                  <label>Total Amount</label>
                  <div class="input-group">
                    <span class="input-group-text">₦</span>
                    <input type="text" id="total" class="form-control" readonly>
                  </div>
                </div>
                
                <button type="button" id="payButton" class="btn btn-primary btn-lg" onclick="initiatePayment()">
                  <i class="fa fa-credit-card"></i> Pay with Payvessel
                </button>
              </div>
              
              <div class="col-md-6">
                <div class="card bg-light">
                  <div class="card-body">
                    <h5 class="card-title">Payment Information</h5>
                    <ul class="list-unstyled">
                      <li><i class="fa fa-check text-success"></i> Secure payment processing</li>
                      <li><i class="fa fa-check text-success"></i> Instant wallet funding</li>
                      <li><i class="fa fa-check text-success"></i> 1.5% processing fee</li>
                      <li><i class="fa fa-check text-success"></i> Maximum ₦5000 per transaction</li>
                      <li><i class="fa fa-check text-success"></i> All major cards accepted</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            
          <?php endif; ?>
          
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Calculate charges and total
document.getElementById('amount').addEventListener('input', function() {
  const amount = parseFloat(this.value) || 0;
  const charge = amount * 0.015; // 1.5% charge
  const total = amount + charge;
  
  document.getElementById('charge').value = charge.toFixed(2);
  document.getElementById('total').value = total.toFixed(2);
});

function initiatePayment() {
  const amount = parseFloat(document.getElementById('amount').value);
  
  if (!amount || amount < 100) {
    Swal.fire('Invalid Amount', 'Please enter an amount between ₦100 and ₦5000', 'error');
    return;
  }
  
  if (amount > 5000) {
    Swal.fire('Amount Too High', 'Maximum amount allowed is ₦5000', 'error');
    return;
  }
  
  // Show loading
  Swal.fire({
    title: 'Processing Payment',
    text: 'Please wait while we redirect you to Payvessel...',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });
  
  // Make AJAX call to create payment
  $.ajax({
    url: 'payvessel_create_payment.php',
    method: 'POST',
    data: {
      amount: amount,
      email: '<?= $data['email'] ?>',
      name: '<?= $data['name'] ?>',
      phone: '<?= $data['phone'] ?>'
    },
    success: function(response) {
      try {
        const result = JSON.parse(response);
        if (result.success) {
          // Redirect to Payvessel payment page
          window.location.href = result.payment_url;
        } else {
          Swal.fire('Error', result.message || 'Failed to create payment', 'error');
        }
      } catch (e) {
        Swal.fire('Error', 'Invalid response from server', 'error');
      }
    },
    error: function() {
      Swal.fire('Error', 'Failed to connect to server', 'error');
    }
  });
}
</script> 