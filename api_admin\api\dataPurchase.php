<?php

$actual_link = $_SERVER['REMOTE_ADDR'];
//file_put_contents('trt.txt', $actual_link);
//if ($actual_link != '198.187.31.83') die('Invalid REQUEST');

include "../../conn.php";

if ($_SERVER["REQUEST_METHOD"] == "GET") {
	http_response_code(405);
	die(json_encode(array('error' => true, 'desc' => 'Unsupported Method')));
}
$headers = apache_request_headers();

if (!isset($_POST['plan_id']) || empty($_POST['plan_id'])) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid plan_id')));
}
if (!isset($_POST['phone_number']) || empty($_POST['phone_number']) || strlen($_POST['phone_number']) != 11) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid phone_number')));
}
if (!isset($_POST['network_id']) || empty($_POST['network_id'])) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid network_id')));
}
$username = mysqli_real_escape_string($con, $_POST['username']);
$phone_number = mysqli_real_escape_string($con, $_POST['phone_number']);
$network_id = mysqli_real_escape_string($con, $_POST['network_id']);
$plan_id = mysqli_real_escape_string($con, $_POST['plan_id']);
$ported_number = (isset($_POST['ported_number']) && $_POST['ported_number'] == 'true') ? true : false;
$network = [];
$nets = mysqli_query($con, "SELECT * FROM networks");
while ($net = mysqli_fetch_assoc($nets)) {
	if ($net['id'] == $network_id) {
		$network[0] = $net['id'];
		$network[1] = $net['name'];
	}
}
if ($network == []) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid network_id')));
}



$net_id = $network[0];
$plan_q = mysqli_query($con, "SELECT * FROM data_plans WHERE id = '$plan_id'");
if (mysqli_num_rows($plan_q) != 1) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid plan_id')));
}



$plan_data = mysqli_fetch_assoc($plan_q);


if (strtolower($plan_data['status']) == 'disable') {
	http_response_code(404);
	die(json_encode(array('error' => true, 'desc' => 'Plan currently not available')));
}

function randomNumber($length)
{
	$result = '';
	for ($i = 0; $i < $length; $i++) {
		$result .= mt_rand(0, 9);
	}
	return "TRANS_" . $result;
}

$type = 'DATA PURCHASE';
$t_desc = $network[1] . " " . $plan_data['plan_type'] . " DATA OF " . $plan_data['plan_size'] . $plan_data['plan_volume'] . " TO " . $phone_number;
$amount = $plan_data['user2_price'];

$status = 'pending';
$t_date = $current_date;
$ref = md5($t_date);
$channel = $network[1];
$n = 1;
$data_type = $plan_data['plan_type'];
$platForms = $plan_data['current_platform'];


$netsToken = mysqli_query($con, "SELECT * FROM apikey WHERE platform='$platForms' AND types='data' ");
$tokenKey = mysqli_fetch_assoc($netsToken);

if (!$tokenKey) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid apikey')));
}

$apiLink = $tokenKey['apiLink'];
$apiKey = $tokenKey['apiKey'];

if (in_array($platForms, ['superjara', 'gongoz', 'maskawa', 'husmodata', 'gladtidingsdata', 'legitdataway', 'n3tdata', 'bilalsadasub', 'edata'])) {

	$spl = $plan_data[$platForms . '_id'];
	$platform = strtoupper($platForms);
	$ref = 'REF_' . randomNumber(25);

	$response =  dataFromSource($phone_number, $spl, strtoupper($network[1]), $apiLink, $apiKey);
	$resp = json_decode($response);

	if ((isset($resp->Status) && ($resp->Status == 'successful' || $resp->Status == 'processing')) || (isset($resp->status) && $resp->status == 'success' || $resp->status == 'successful' || $resp->status->response == 'successful')) {

		$resp = mysqli_real_escape_string($con, $response);
		$status = 'success';
		mysqli_query($con, "INSERT INTO api_transactions (type, detail, t_desc, amount, amount_before, amount_after, username, status, t_date, ref, channel, platform) VALUES ('$type', '$resp', '$t_desc', '$amount', '', '', '', '$status', '$t_date', '$ref', '$channel', '$platform')");
		http_response_code(202);
		die(json_encode(array('error' => false, 'status' => 202, 'desc' => $t_desc . ' Placed Successfully', 'ref' => $ref)));
	} else {
		$status = 'failed';
		$resp = mysqli_real_escape_string($con, $response);
		mysqli_query($con, "INSERT INTO api_transactions (type, detail, t_desc, amount, amount_before, amount_after, username, status, t_date, ref, channel, platform) VALUES ('$type', '$resp', '$t_desc', '$amount', '', '', '', '$status', '$t_date', '$ref', '$channel', '$platform')");
		http_response_code(502);
		die(json_encode(array('error' => true, 'status' => 400, 'desc' => 'Transaction not completed')));
	}
}


function dataFromSource($phone, $pl, $net, $apiLink, $apiKey)
{
	$platforms1 = ['superjara', 'gongoz', 'maskawasub', 'husmodata', 'gladtidingsdata'];
	$platforms2 = ['legitdataway', 'n3tdata', 'bilalsadasub'];
	$platforms3 = ['edata'];

	if (checkPlatforms1($apiLink, $platforms1)) {
		$net_id = null;
		$networks = ['mtn' => 1, 'glo' => 2, '9mobile' => 3, 'airtel' => 4];
		$net = strtolower($net);
		$net_id = $networks[$net] ?? null;
		$pl = intval($pl);
		$js = json_encode(['network' => $net_id, 'mobile_number' => $phone, 'plan' => $pl, 'Ported_number' => true]);
		$payload = ['Authorization: Token ' . $apiKey, 'Content-Type: application/json'];
	} elseif (checkPlatforms2($apiLink, $platforms2)) {
		$net_id = null;
		$randomString = 'Data_' . mt_rand(10000000000, 99999999999);
		$networks = ['mtn' => 1, 'glo' => 3, '9mobile' => 4, 'airtel' => 3];
		$net = strtolower($net);
		$net_id = $networks[$net] ?? null;
		$pl = intval($pl);
		$js = json_encode(['network' => $net_id, 'phone' => $phone, 'data_plan' => $pl, 'bypass' => true, 'request-id' => $randomString]);
		$payload = ['Authorization: Token ' . $apiKey, 'Content-Type: application/json'];
	} elseif (checkPlatforms2($apiLink, $platforms3)) {
		$net_id = null;
		$networks = ['mtn' => 1, 'glo' => 3, '9mobile' => 4, 'airtel' => 2];
		$net = strtolower($net);
		$net_id = $networks[$net] ?? null;
		$pl = intval($pl);
		$js = json_encode(['network_id' => $net_id, 'phone' => $phone, 'plan_id' => $pl]);
		$payload = ['Authorization: Bearer ' . $apiKey, 'Content-Type: application/json'];
	} else {
		http_response_code(400);
		die(json_encode(['error' => true, 'desc' => 'Invalid platform' . $apiLink]));
	}

	$curl = curl_init();
	curl_setopt_array($curl, array(
		CURLOPT_URL => $apiLink,
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => $js,
		CURLOPT_HTTPHEADER => $payload,
	));
	$response = curl_exec($curl);
	curl_close($curl);
	//file_put_contents('dataRes.txt', "$response");
	return $response;
}

function checkPlatforms1($apiLink, $platforms1)
{
	foreach ($platforms1 as $platform) {
		if (strpos($apiLink, $platform) !== false) {
			return true;
		}
	}
	return false;
}

function checkPlatforms2($apiLink, $platforms2)
{
	foreach ($platforms2 as $platform) {
		if (strpos($apiLink, $platform) !== false) {
			return true;
		}
	}
	return false;
}

function checkPlatforms3($apiLink, $platforms3)
{
	foreach ($platforms3 as $platform) {
		if (strpos($apiLink, $platform) !== false) {
			return true;
		}
	}
	return false;
}
