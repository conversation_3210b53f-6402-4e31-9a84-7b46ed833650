# Payvessel Payment Gateway Integration Guide

## Overview

This guide explains how to integrate Payvessel payment gateway into your existing payment system. The integration follows the same pattern as your existing Monnify and Paystack implementations.

## Files Created/Modified

### 1. Admin Configuration
- **File:** `secret_admin_link_dina/credentials.php`
- **Purpose:** Admin interface to configure Payvessel credentials
- **Configuration:** Public Key, Secret Key, Merchant ID

### 2. Payment Interface
- **File:** `web/app-/payvessel.php`
- **Purpose:** User-facing payment page for Payvessel
- **Features:** Amount input, fee calculation, payment processing

### 3. Virtual Account System
- **File:** `web/app-/payvessel_virtual_account.php`
- **Purpose:** Virtual account creation and management for KYC-verified users
- **Features:** Account creation, account display, copy functionality
- **File:** `web/app-/payvessel_create_accounts.php`
- **Purpose:** Backend handler for creating virtual accounts via Payvessel API

### 4. Payment Handlers
- **File:** `web/app-/handler.php` (modified)
- **Purpose:** API-based payment verification
- **File:** `web/app-/payvessel_callback.php`
- **Purpose:** Callback-based payment verification

### 5. Webhook Handler
- **File:** `payvesselWebHook.php`
- **Purpose:** Server-to-server webhook processing
- **Security:** IP verification and signature validation

### 6. Navigation
- **File:** `web/app-/header.php` (modified)
- **Purpose:** Added Payvessel and Virtual Account options to navigation menu

## Configuration Steps

### Step 1: Database Setup

**Option A: Run the migration script**
1. Navigate to: `database_migration_payvessel.php`
2. Run the script in your browser
3. Follow the on-screen instructions

**Option B: Manual SQL commands**
1. Run these SQL commands in your database:
```sql
ALTER TABLE config ADD COLUMN payvessel TEXT DEFAULT NULL;
ALTER TABLE users ADD COLUMN payvessel_accounts TEXT DEFAULT NULL;
```

### Step 2: Admin Configuration

1. Login to your admin panel: `secret_admin_link_dina/`
2. Navigate to "Credentials Update" section
3. Fill in Payvessel configuration:
   - **API Key:** Your Payvessel API key (format: PVKEY-...)
   - **API Secret:** Your Payvessel API secret (format: PVSECRET-...)
4. Click "Change" to save

### Step 3: Webhook Setup

1. In your Payvessel dashboard, set the webhook URL to:
   ```
   https://yourdomain.com/payvesselWebHook.php
   ```

2. Configure webhook events for:
   - Payment success
   - Payment failure
   - Payment pending
   - Virtual account payments

### Step 4: Virtual Account Setup

1. Ensure users complete KYC verification (BVN and NIN required)
2. Users can access virtual accounts via "Virtual Account (Payvessel)" menu
3. Virtual accounts are automatically created for KYC-verified users

### Step 5: Database Configuration

The system automatically stores Payvessel configuration in the `config` table as JSON:
```json
["api_key", "api_secret"]
```

Virtual accounts are stored in the `users` table in the `payvessel_accounts` column as JSON.

## Payment Flow

### Frontend Flow (User Experience)

#### Card Payment Flow:
1. User navigates to "ATM Automatic Funding (Payvessel)"
2. User enters amount (minimum ₦100)
3. System calculates total with 1.5% processing fee
4. User clicks "Pay with Payvessel"
5. Payvessel payment modal opens
6. User completes payment
7. System verifies payment and updates wallet

#### Virtual Account Flow:
1. User completes KYC verification (BVN and NIN)
2. User navigates to "Virtual Account (Payvessel)"
3. User clicks "Create Virtual Accounts"
4. System creates virtual accounts via Payvessel API
5. User receives account numbers for bank transfers
6. User transfers money to any virtual account
7. System receives webhook and updates wallet automatically

### Backend Flow (Payment Processing)

#### Option 1: Callback Verification
1. Payvessel sends webhook to `payvesselWebHook.php`
2. System verifies webhook signature
3. System verifies transaction with Payvessel API
4. System updates user balance and records transaction

#### Option 2: API Verification
1. Frontend calls `handler.php` with payment reference
2. System verifies transaction with Payvessel API
3. System updates user balance and records transaction

## Security Features

### 1. Webhook Security
- IP whitelisting (configure Payvessel's IP addresses)
- Signature verification using HMAC-SHA256
- Duplicate transaction prevention

### 2. API Security
- Bearer token authentication
- Transaction reference verification
- Amount validation

### 3. Database Security
- Prepared statements to prevent SQL injection
- Input sanitization and validation
- Transaction logging for audit trail

## Error Handling

### Common Error Scenarios

1. **Configuration Missing**
   - Error: "Payvessel is not configured"
   - Solution: Configure Payvessel in admin panel

2. **Invalid Amount**
   - Error: "Please enter an amount of ₦100 or more"
   - Solution: Enter valid amount

3. **Payment Failed**
   - Error: "Payment failed or pending"
   - Solution: Check payment status and retry

4. **User Not Found**
   - Error: "User not found for email"
   - Solution: Verify user email in database

## Testing

### Test Mode Configuration

1. Set `isTestMode: true` in `payvessel.php` for testing
2. Use Payvessel test credentials
3. Test with small amounts (₦100-₦500)

### Production Mode Configuration

1. Set `isTestMode: false` in `payvessel.php`
2. Use Payvessel live credentials
3. Configure webhook URL for production

## API Endpoints

### Payvessel API Integration

```php
// Transaction Verification
$url = "https://api.payvessel.com/transaction/verify/$reference";
$headers = [
    "Authorization: Bearer $secretKey",
    "Cache-Control: no-cache"
];
```

### Webhook Processing

```php
// Webhook URL
https://yourdomain.com/payvesselWebHook.php

// Expected Payload
{
    "reference": "TXN_123456789",
    "status": "success",
    "amount": 150000, // in kobo
    "customer": {
        "email": "<EMAIL>"
    }
}
```

## Database Schema

### Configuration Storage
```sql
-- config table
payvessel: ["api_key", "api_secret"]
```

### Transaction Recording
```sql
-- transactions table
INSERT INTO transactions (
    ref, type, status, amount, oldBal, newBal, 
    email, date_time, t_desc
) VALUES (
    'TXN_123456789', 'Wallet Funding', 'success', 
    1500.00, 1000.00, 2500.00, 
    '<EMAIL>', '2024-01-01 12:00:00', 
    'Wallet funded via Payvessel'
);
```

## Monitoring and Logging

### Error Logging
- All payment errors are logged to PHP error log
- Webhook processing errors are logged
- API verification errors are logged

### Success Logging
- Successful transactions are logged
- Balance updates are recorded
- Transaction references are stored

## Troubleshooting

### Common Issues

1. **Webhook Not Receiving**
   - Check webhook URL configuration
   - Verify server can receive POST requests
   - Check firewall settings

2. **Payment Not Processing**
   - Verify Payvessel credentials
   - Check API endpoint availability
   - Review error logs

3. **Balance Not Updated**
   - Check database connection
   - Verify user email exists
   - Review transaction logs

### Debug Mode

Enable debug logging by adding:
```php
error_log("Payvessel Debug: " . json_encode($data));
```

## Support

For Payvessel-specific issues:
1. Check Payvessel documentation
2. Contact Payvessel support
3. Review API response codes

For integration issues:
1. Check error logs
2. Verify configuration
3. Test with small amounts first

## Security Best Practices

1. **Never expose secret keys** in frontend code
2. **Always verify webhook signatures**
3. **Use HTTPS** for all API calls
4. **Implement IP whitelisting** for webhooks
5. **Log all payment activities** for audit
6. **Use prepared statements** for database queries
7. **Validate all input data** before processing
8. **Implement rate limiting** for API calls

## Performance Optimization

1. **Cache configuration** to reduce database queries
2. **Use connection pooling** for database
3. **Implement timeout handling** for API calls
4. **Use async processing** for webhooks when possible
5. **Monitor response times** and optimize slow queries

---

**Note:** This integration follows the same pattern as your existing Monnify and Paystack implementations, ensuring consistency and maintainability across your payment gateway system. 