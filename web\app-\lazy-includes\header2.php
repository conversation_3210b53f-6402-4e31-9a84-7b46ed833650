<?php
// error_reporting(0);
include "../../config.php";

if (!isset($_SESSION['username'])) {
  header('location:logout');
}
if ($_SESSION['token'] !== "1e8789816530b40d8784c371d829db38") {
  header('location:login.php');
}
if (!isset($_SESSION['LAST_ACTIVITY'])) {
  header('location:login.php');
}
if (time() - $_SESSION['LAST_ACTIVITY'] > 300000) {
  $last = $_SERVER['REQUEST_URI'];
  header("location:?last={$last}");
}
$_SESSION['LAST_ACTIVITY'] = time();
function parseAmt($amt)
{
  $val = intval($amt);
  if ($val > 999999) {
    return substr_replace(substr_replace($val, ',', -3, 0), ',', -7, 0);
  } elseif ($val > 999) {
    return substr_replace($val, ',', -3, 0);
  } else {
    return $val;
  }
}




?>





<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="">
  <meta name="author" content="">
  <meta name="generator" content="">
  <title><?= $config['site_name'] ?></title>


  <!-- Favicons -->
  <link rel="apple-touch-icon" href="assets/img/sharesub-logo.png" sizes="180x180">
  <link rel="apple-touch-icon" href="assets/img/sharesub-logo.png" sizes="152x152">
  <link rel="apple-touch-icon" href="assets/img/sharesub-logo.png" sizes="167x167">
  <link rel="apple-touch-icon" href="assets/img/sharesub-logo.png" sizes="120x120">
  <link rel="icon" href="assets/img/favion32.png" sizes="32x32" type="image/png">
  <link rel="icon" href="assets/img/favion16.png" sizes="16x16" type="image/png">
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/slick-loader@1.1.20/slick-loader.min.css">
  <script src="https://unpkg.com/slick-loader@1.1.20/slick-loader.min.js"></script>

  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.1.7/dist/sweetalert2.min.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.1.7/dist/sweetalert2.all.min.js"></script>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

  <link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>

  <!-- Google fonts-->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
  <script src="lazy-dev/assets/js/plugin/webfont/webfont.min.js"></script>
  <script>
    WebFont.load({
      google: {
        "families": ["Lato:300,400,700,900"]
      },
      custom: {
        "families": ["Flaticon", "Font Awesome 5 Solid", "Font Awesome 5 Regular", "Font Awesome 5 Brands", "simple-line-icons"],
        urls: ['lazy-dev/assets/css/fonts.min.css']
      },
      active: function() {
        sessionStorage.fonts = true;
      }
    });
  </script>
  <style>
    html {
      scroll-behavior: smooth;
    }
    
    /* Header Styles for Dark Mode */
    .dark-mode .header {
      background-color: #1a1a1a !important;
      border-color: #333 !important;
    }
    
    .dark-mode .header h5, 
    .dark-mode .header span,
    .dark-mode .header .text-secondary {
      color: white !important;
    }
    
    .dark-mode .btn-light {
      background-color: #333 !important;
      border-color: #444 !important;
      color: white !important;
    }
    
    .dark-mode .btn-light:hover {
      background-color: #444 !important;
    }
    
    .dark-mode .btn-light i,
    .dark-mode .btn-light .bi {
      color: white !important;
    }
  </style>
  <!-- CSS Files -->
  <link href="assets/bootstrap.min.css" rel="stylesheet">


  <!-- bootstrap icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">

  <!-- swiper carousel css -->
  <link rel="stylesheet" href="assets/vendor/swiperjs-6.6.2/swiper-bundle.min.css">

  <!-- style css for this template -->
  <link href="assets/css/style.css" rel="stylesheet" id="style">
  
  <!-- Dark Mode CSS -->
  <link href="assets/css/dark-mode.css" rel="stylesheet">
  
  <!-- Button Styles CSS -->
  <link href="assets/css/button-styles.css" rel="stylesheet">
  
  <script>
    // Check for dark mode preference in cookie
    function getCookie(cname) {
      let name = cname + "=";
      let decodedCookie = decodeURIComponent(document.cookie);
      let ca = decodedCookie.split(';');
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) == ' ') {
          c = c.substring(1);
        }
        if (c.indexOf(name) == 0) {
          return c.substring(name.length, c.length);
        }
      }
      return "";
    }
    
    // Add dark-mode class to HTML tag if enabled
    if (getCookie("fwalayoutmode") === 'dark-mode') {
      document.documentElement.classList.add('dark-mode');
    }
  </script>
</head>

<body class="body-scroll" data-page="">

  <!-- loader section -->
  <!--
  <div class="container-fluid loader-wrap">
    <div class="row h-100">
      <div class="col-10 col-md-6 col-lg-5 col-xl-3 mx-auto text-center align-self-center">
        <div class="logo-wallet">
          <div class="wallet-bottom">
          </div>
          <div class="wallet-cards"></div>
          <div class="wallet-top">
          </div>
        </div>
        <p class="mt-4"><span class="text-secondary">Loading <?= $config['site_name'] ?></span><br><strong>Please
            Wait...</strong></p>
      </div>
    </div>
  </div>
  -->
  <!-- loader section ends -->

  <!-- Begin page -->
  <main class="h-100">

    <!-- Header -->
    <header class="header position-fixed">
      <div class="row">
        <div class="col-auto">
          <button type="button" class="btn btn-light btn-44 back-btn">
            <i class="bi bi-arrow-left"></i>
          </button>
        </div>
        <div class="col text-center">
          <div class="logo-small">
            <!-- <img src="assets/img/logo.png" alt="" /> -->
            <h5><?= $config['site_name'] ?> <br /><span class="text-secondary fw-light">Mobile App</span></h5>
          </div>
        </div>
        <div class="col-auto">
          <div class="d-flex align-items-center">
            <div class="form-check form-switch me-2">
              <input class="form-check-input" type="checkbox" id="darkmodeswitch">
              <label class="form-check-label d-none d-sm-block" for="darkmodeswitch"></label>
            </div>
            <a href="profile.php" target="_self" class="btn btn-light btn-44">
              <i class="bi bi-person-circle"></i>
              <span class="count-indicator"></span>
            </a>
          </div>
        </div>
      </div>
    </header>
    <!-- Header ends -->
    
    <script>
      // Back button functionality
      document.addEventListener('DOMContentLoaded', function() {
        const backBtn = document.querySelector('.back-btn');
        if (backBtn) {
          backBtn.addEventListener('click', function() {
            // Check if there's a previous page in history
            if (window.history.length > 1) {
              window.history.back();
            } else {
              // If no history, redirect to welcome page
              window.location.href = 'welcome.php';
            }
          });
        }
      });
    </script>