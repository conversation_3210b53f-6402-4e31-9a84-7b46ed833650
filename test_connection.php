<?php
// Test script to verify database connection and file paths
echo "<h2>Database Connection Test</h2>";

// Test 1: Check if conn.php exists and works
echo "<h3>Test 1: Database Connection</h3>";
try {
    include 'conn.php';
    if (isset($con) && $con) {
        echo "<p style='color: green;'>✅ Database connection successful</p>";
        
        // Test query
        $result = mysqli_query($con, "SELECT COUNT(*) as count FROM config");
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            echo "<p>Config table has " . $row['count'] . " records</p>";
        } else {
            echo "<p style='color: red;'>❌ Error querying config table</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

// Test 2: Check Payvessel configuration
echo "<h3>Test 2: Payvessel Configuration</h3>";
try {
    $config = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM config"));
    if (isset($config['payvessel'])) {
        $payvesselConfig = json_decode($config['payvessel'], true);
        if ($payvesselConfig && count($payvesselConfig) >= 2) {
            echo "<p style='color: green;'>✅ Payvessel configured</p>";
            echo "<p>API Key: " . substr($payvesselConfig[0], 0, 10) . "...</p>";
            echo "<p>API Secret: " . substr($payvesselConfig[1], 0, 10) . "...</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Payvessel not configured</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Payvessel column not found in config table</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking Payvessel config: " . $e->getMessage() . "</p>";
}

// Test 3: Check users table structure
echo "<h3>Test 3: Users Table Structure</h3>";
try {
    $result = mysqli_query($con, "DESCRIBE users");
    $columns = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $columns[] = $row['Field'];
    }
    
    if (in_array('payvessel_accounts', $columns)) {
        echo "<p style='color: green;'>✅ payvessel_accounts column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ payvessel_accounts column missing</p>";
    }
    
    if (in_array('bvn', $columns)) {
        echo "<p style='color: green;'>✅ bvn column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ bvn column missing</p>";
    }
    
    if (in_array('nin', $columns)) {
        echo "<p style='color: green;'>✅ nin column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ nin column missing</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking users table: " . $e->getMessage() . "</p>";
}

// Test 4: Check file paths
echo "<h3>Test 4: File Paths</h3>";
$files_to_check = [
    'conn.php' => 'Database connection file',
    'web/app-/payvessel_virtual_account.php' => 'Virtual account page',
    'web/app-/payvessel_create_accounts.php' => 'Account creation handler',
    'web/app-/payvessel.php' => 'Payvessel payment page',
    'web/app-/handler.php' => 'Payment handler',
    'payvesselWebHook.php' => 'Webhook handler'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $description missing: $file</p>";
    }
}

echo "<hr>";
echo "<h3>Summary</h3>";
echo "<p>If all tests pass, your Payvessel integration should work correctly.</p>";
echo "<p><a href='web/app-/payvessel_virtual_account.php' class='btn btn-primary'>Test Virtual Account Page</a></p>";
?> 