<?php include('lazy-includes/header2.php');
$usertype = $data['type'] . '_price';
$dPlan = [];
$mtnsme = [];

$mtnsmeq = mysqli_query($con, "SELECT id, size, amount, type FROM data_card_price WHERE network = 'MTN' ORDER BY `amount` ASC");
while ($pl = mysqli_fetch_array($mtnsmeq)) {
  array_push($dPlan, [$pl['id'], $pl['size'], $pl['amount'], $pl['type']]);
  array_push($mtnsme, [$pl['id'], $pl['size'], $pl['amount'], $pl['type'], '30Days',]);
}

$airtelcg = [];
$airtelcgq = mysqli_query($con, "SELECT id, size, amount, type FROM data_card_price WHERE network = 'AIRTEL' ORDER BY `amount` ASC");
while ($pl = mysqli_fetch_array($airtelcgq)) {
  array_push($dPlan, [$pl['id'], $pl['size'], $pl['amount'], $pl['type']]);
  array_push($airtelcg, [$pl['id'], $pl['size'], $pl['amount'], $pl['type'], '30Days',]);
}

$glocg = [];
$glocgq = mysqli_query($con, "SELECT id, size, amount, type FROM data_card_price WHERE network = 'GLO' ORDER BY `amount` ASC");
while ($pl = mysqli_fetch_array($glocgq)) {
  array_push($dPlan, [$pl['id'], $pl['size'], $pl['amount'], $pl['type']]);
  array_push($glocg, [$pl['id'], $pl['size'], $pl['amount'], $pl['type'], '30Days',]);
}


function sortPlans($pl, $net, $type)
{
  $plans = json_decode($pl);
  if (!is_array($plans)) {
    return "'Not Available'";
  }
  return json_encode($plans);
}

?>



<!-- main page content -->
<div class="main-container container">


  <div class="row">
    <div class="col-md-12">
      <div class="card">


        <div class="card-header">
          <div class="card-title">
            DATA CARD
          </div>
        </div>





        <div class="card-body">
          <div class="row">
            <div class="colI-md-6I colI-lg-4">



              <div class="row mb-1 justify-content-center">
                <div class="col-12 col-md-8 col-lg-6">


                  <!-- start -->

                  <link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">
                  <script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>



                  <!-- <form method="POST" class="forms-sample"> -->
                  <div class="form-group">
                    <label>Network</label>
                    <select class="form-control" id="net" oninput="netChange(this.value, document.getElementById('data_size').value)" name="network" required>
                      <option value="" selected="" disabled>Choose Network</option>
                      <option value="mtn">MTN</option>
                      <option value="airtel">AIRTEL</option>
                      <option value="glo">GLO</option>
                      <option value="9mobile" disabled>9MOBILE</option>
                    </select>
                  </div>
                  <!-- <div class="form-group" id="datType">
            <label>Type</label>
            <select class="form-control" type="hidden" id="dtyp" oninput="typeChange(document.getElementById('net').value, this.value)" name="type" required>
            </select>
          </div> -->
                  <div class="form-group">
                    <label>Size</label>
                    <select id="data_size" class="form-control" name="size" required>

                    </select>
                  </div>
                  <div class="form-group">
                    <label>Quantity</label>
                    <input type="text" autocomplete="off" id="q" name="q" class="form-control" placeholder="Quantity" required />
                  </div>
                  <div class="form-group">
                    <label>Card Name</label>

                    <input type="text" autocomplete="off" id="phone" name="phone" class="form-control" placeholder="E.g: Your Name...." required />
                  </div>
                  <button type="button" name="topup" onclick="topup()" class="w-100 p-3 btn btn-primary mr-2"> PROCEED</button>



                  <br><br>









                  <br>

                  <hr>

                  <center>
                    <h6>Codes To Check Data Balance: </h6>
                  </center>
                  <br>
                  <ul class="list-group">
                    <li class="list-group-item list-group-item-light">MTN: <b>*131*4# || *460*260# || *461*4#</b> </li>
                    <li class="list-group-item list-group-item-light"> Airtel: *140# </li>
                  </ul>
                </div>




                <script type="text/javascript">
                  var special = ['zeroth', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'eighth', 'ninth', 'tenth', 'eleventh', 'twelfth', 'thirteenth', 'fourteenth', 'fifteenth', 'sixteenth', 'seventeenth', 'eighteenth', 'nineteenth'];
                  var deca = ['twent', 'thirt', 'fort', 'fift', 'sixt', 'sevent', 'eight', 'ninet'];

                  function stringifyNumber(n) {
                    if (n < 20) return special[n];
                    if (n % 10 === 0) return deca[Math.floor(n / 10) - 2] + 'ieth';
                    return deca[Math.floor(n / 10) - 2] + 'y-' + special[n % 10];
                  }
                  let mtn_size = <?= json_encode($mtnsme) ?>;
                  let airtel_cg_size = <?= json_encode($airtelcg) ?>;
                  let glo_cg_size = <?= json_encode($glocg) ?>;



                  let smests = 'true'; //'<?= $_SESSION['config']['mtn_sme_data'] ?>';
                  let cgAirsts = 'true'; //'<?= $_SESSION['config']['air_cg_data'] ?>';
                  let cgGlosts = 'true'; //'<?= $_SESSION['config']['glo_cg_data'] ?>';
                  let gfMbsts = 'false'; //'<?= $_SESSION['config']['air_cg_data'] ?>';

                  let all_p = <?= json_encode($dPlan) ?>;

                  function netChange(net, typ) {
                    const dropdown = document.getElementById('data_size');
                    dropdown.innerHTML = '';
                    const chooseTypeOption = document.createElement('option');
                    chooseTypeOption.value = '';
                    chooseTypeOption.disabled = true;
                    chooseTypeOption.selected = true;
                    chooseTypeOption.textContent = 'Choose Type';
                    dropdown.appendChild(chooseTypeOption);

                    if (net === 'mtn') {
                      // if (smests === 'true') {
                      //   addOption(dropdown, 'SME', 'SME');
                      // }
                      // if (typ === 'SME') {
                      changeSize(mtn_size);
                      // }
                    } else if (net === 'airtel') {
                      // if (cgAirsts === 'true') {
                      //   addOption(dropdown, 'CDG', 'CORPORATE GIFTING');
                      // }
                      // if (typ === 'CDG') {
                      changeSize(airtel_cg_size);
                      //}
                    } else if (net === 'glo') {
                      // if (cgGlosts === 'true') {
                      //   addOption(dropdown, 'CDG', 'CORPORATE GIFTING');
                      // }
                      // if (typ === 'CDG') {
                      changeSize(glo_cg_size);
                      // }
                    } else if (net === '9mobile') {
                      // if (gfMbsts === 'true') {
                      //   addOption(dropdown, 'CDG', 'CORPORATE GIFTING');
                      // }
                      // if (typ === 'CDG') {
                      //changeSize(mobile_size);
                      //}
                    }
                  }

                  function changeSize(size) {
                    const dropdown = document.getElementById('data_size');
                    dropdown.innerHTML = '';
                    const chooseSizeOption = document.createElement('option');
                    chooseSizeOption.value = '';
                    chooseSizeOption.disabled = true;
                    chooseSizeOption.selected = true;
                    chooseSizeOption.textContent = 'Choose Size';
                    dropdown.appendChild(chooseSizeOption);

                    if (size.length > 0) {
                      for (let i = 0; i < size.length; i++) {
                        const option = document.createElement('option');
                        option.value = size[i][0];
                        option.textContent = `${size[i][1]} @ ₦${size[i][2]}`;
                        dropdown.appendChild(option);
                      }
                    } else {
                      const notAvailableOption = document.createElement('option');
                      notAvailableOption.textContent = 'Not Available';
                      notAvailableOption.disabled = true;
                      dropdown.appendChild(notAvailableOption);
                    }
                  }

                  function addOption(dropdown, value, text) {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = text;
                    dropdown.appendChild(option);
                  }

                  function typeChange(net, typ) {
                    if (net == 'mtn') {
                      // if (typ == 'SME') {
                      changeSize(mtn_size);
                      // }
                    } else if (net == 'airtel') {
                      // if (typ == 'CDG') {
                      changeSize(airtel_cg_size);
                      //  }
                    } else if (net == 'glo') {
                      // if (typ == 'CDG') {
                      changeSize(glo_cg_size);
                      //  }
                    } else if (net == '9mobile') {

                      //  if (typ == 'CDG') {
                      // changeSize(mobile_cg_size);
                      // }
                    }
                  }
                  // const mtnEx = "<?= $config['mtnEx'] ?>";
                  // const airtelEx = "<?= $config['airtelEx'] ?>";



                  function topup() {
                    let net = document.getElementById('net').value;
                    let dtyp = 'CG';
                    let data_size = document.getElementById('data_size').value;
                    let phone = document.getElementById('phone').value;
                    let q = document.getElementById('q').value;

                    function beginTop() {
                      let dPlan = '';
                      all_p.forEach((p) => {
                        if (p[0] == data_size) {
                          dPlan = p[1]
                        }
                      })
                      Swal.fire({
                        title: 'Are you sure to continue?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonText: "Yes, I'm Sure",
                        cancelButtonText: 'No'
                      }).then((res) => {
                        if (res.isConfirmed) {
                          SlickLoader.setText("Loading...", "Please Wait");
                          SlickLoader.enable();
                          $.post('CardTopUp', {
                            network: net,
                            data_plan: data_size,
                            phone: phone,
                            topUp: dtyp,
                            q: q
                          }, (r) => {
                            SlickLoader.disable();
                            console.log(r);
                            if (r == '200') {
                              Swal.fire({
                                title: 'Data Card Successful',
                                text: `YOU HAVE SUCCESSFUL PURCHASED ${net.toUpperCase()} DATA CARD ${dPlan} AS ${phone}`,
                                icon: 'success',
                                showCancelButton: true,
                                confirmButtonText: 'Okay',
                                cancelButtonText: 'View Receipt',
                                reverseButtons: true
                              }).then((result) => {
                                if (result.isConfirmed) {
                                  // Do nothing
                                } else if (result.dismiss === Swal.DismissReason.cancel) {
                                  window.location.href = `data_card_print?id=${r}`;
                                }
                              });
                            } else if (r == '404') {
                              Swal.fire('Something is missing', '', 'error')
                            } else if (r == '302') {
                              Swal.fire('INSUFFICIENT FUND', '', 'error')
                            } else if (r == '009') {
                              Swal.fire('Invalid Phone Number', '', 'error')
                            } else if (r == '329') {
                              Swal.fire('Wrong PIN', 'Please check and try again', 'error')
                            } else if (r == '732') {
                              Swal.fire('Daily Limit Exceed', `You have exceeded the daily limit and you must verify your account to continue for today. Press OK to go to Account Verification Page`, 'error').then((ress) => {
                                if (ress.isConfirmed) {
                                  window.location.href = 'kyc';
                                }
                              })
                            } else if (r == '552') {
                              Swal.fire('UNKNOWN ERROR', 'Error 504 | Please contact the admin if the error persist', 'error');
                            } else if (r == '403') {
                              Swal.fire('TECHNICAL ERROR', 'Please Contact Admin', 'error')
                            } else if (r == '558') {
                              Swal.fire(`IT SEEMS ${net.toUpperCase()} ${dtyp.toUpperCase()} DATA IS CURRENTLY DOWN`, 'Please Try Again Later', 'error')
                            } else {
                              Swal.fire({
                                title: 'Data Card Successful',
                                text: `YOU HAVE SUCCESSFUL PURCHASED ${net.toUpperCase()} DATA CARD ${dPlan} AS ${phone}`,
                                icon: 'success',
                                showCancelButton: true,
                                confirmButtonText: 'Okay',
                                cancelButtonText: 'View Card',
                                reverseButtons: true
                              }).then((result) => {
                                if (result.isConfirmed) {
                                  // Do nothing
                                } else if (result.dismiss === Swal.DismissReason.cancel) {
                                  window.location.href = `data_card_print?id=${r}`;
                                }
                              });
                            }
                          })

                        } else {
                          Swal.fire('Cancelled!😔')
                        }
                      })
                    }
                    if (net == '') {
                      return Swal.fire('Please Choose Network', '', 'error')
                    } else if (dtyp == '') {
                      return Swal.fire('Please Choose Data Type', '', 'error')
                    } else if (data_size == '') {
                      return Swal.fire('Please Choose Data Size', '', 'error')
                    } else if (phone == '') {
                      return Swal.fire('Type Card Name', '', 'error')
                    } else if (phone.length > 1) {
                      beginTop()
                    } else {
                      if (validateNumber(phone, net)) {
                        beginTop();
                      } else {
                        Swal.fire(`This is not a valid ${net.toUpperCase()} Number`, '', 'error')
                      }
                    }

                  }
                </script>



                <!-- end -->

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>






<!-- main page content ends -->



<?php 
// Get primary WhatsApp number for widget
$whatsapp_numbers = [];
if (isset($config['whatsapp_num']) && !empty($config['whatsapp_num'])) {
    // Try decoding as JSON array first
    $decoded_whatsapp = json_decode($config['whatsapp_num'], true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_whatsapp)) {
        $whatsapp_numbers = $decoded_whatsapp;
    } elseif (is_string($config['whatsapp_num'])) {
         // Fallback: treat as a single number if not valid JSON
        $whatsapp_numbers = [$config['whatsapp_num']];
    }
}

// Get primary WhatsApp number for widget
$primary_whatsapp = !empty($whatsapp_numbers) ? $whatsapp_numbers[0] : '';
// Clean the number (remove non-digits)
$primary_whatsapp_clean = preg_replace('/[^0-9]/', '', $primary_whatsapp);
// Ensure it starts with country code, assuming 234 if it starts with 0 and is local length
if (substr($primary_whatsapp_clean, 0, 1) === '0' && (strlen($primary_whatsapp_clean) == 11 || strlen($primary_whatsapp_clean) == 10)) {
    $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 1);
} elseif (substr($primary_whatsapp_clean, 0, 3) === '234' && strlen($primary_whatsapp_clean) > 13) {
    // Handle cases like +2340... by removing the 0
    $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 3);
} elseif (substr($primary_whatsapp_clean, 0, 1) === '+') {
    $primary_whatsapp_clean = substr($primary_whatsapp_clean, 1); // Remove leading + if present
}
?>

<?php if (!empty($primary_whatsapp_clean)): ?>
<div class="whatsapp-widget">
    <!-- Floating chat button -->
    <button class="whatsapp-button" id="openWhatsappChat">
        <i class="bi bi-whatsapp"></i>
    </button>

    <!-- Chat popup -->
    <div class="whatsapp-popup" id="whatsappChatPopup">
        <div class="popup-header">
            <div class="popup-header-content">
                <img src="../../sharesublogo.png" alt="<?= htmlspecialchars($config['site_name']) ?>" width="40">
                <div>
                    <h6 class="mb-0"><?= htmlspecialchars($config['site_name']) ?> Support</h6>
                    <small class="text-muted">Usually replies within an hour</small>
                </div>
            </div>
            <button class="close-btn" id="closeWhatsappChat">×</button>
        </div>
        <div class="popup-body">
            <div class="message-container">
                <div class="received-message">
                    <p>Hello! How can we help you today?</p>
                    <small class="message-time">Now</small>
                </div>
            </div>
            <div class="message-input">
                <form id="whatsappMessageForm">
                    <input type="text" id="whatsappMessage" placeholder="Type a message..." required>
                    <button type="submit">
                        <i class="bi bi-send-fill"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- WhatsApp Widget Styles -->
<style>
    .whatsapp-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }

    .whatsapp-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #25D366;
        color: white;
        border: none;
        font-size: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        transition: all 0.3s;
    }

    .whatsapp-button:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 14px rgba(0, 0, 0, 0.2);
    }

    .whatsapp-popup {
        position: absolute;
        bottom: 80px;
        right: 0;
        width: 320px;
        background-color: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
        display: none;
    }

    .popup-header {
        background-color: #25D366;
        color: white;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .popup-header-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .popup-header img {
        border-radius: 50%;
        background-color: white;
        padding: 5px;
    }

    .close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
    }

    .popup-body {
        display: flex;
        flex-direction: column;
        height: 300px;
    }

    .message-container {
        flex-grow: 1;
        padding: 15px;
        overflow-y: auto;
    }

    .received-message,
    .sent-message {
        max-width: 80%;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 10px;
    }

    .received-message {
        background-color: #f0f0f0;
        align-self: flex-start;
    }

    .sent-message {
        background-color: #dcf8c6;
        align-self: flex-end;
        margin-left: auto;
    }

    .message-time {
        display: block;
        font-size: 10px;
        margin-top: 5px;
        opacity: 0.6;
    }

    .message-input {
        border-top: 1px solid #e0e0e0;
        padding: 10px;
    }

    .message-input form {
        display: flex;
    }

    .message-input input {
        flex-grow: 1;
        border: none;
        padding: 10px;
        outline: none;
    }

    .message-input button {
        background-color: transparent;
        border: none;
        color: #25D366;
        cursor: pointer;
        font-size: 20px;
    }
</style>

<!-- WhatsApp Widget Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const openBtn = document.getElementById('openWhatsappChat');
        const closeBtn = document.getElementById('closeWhatsappChat');
        const popup = document.getElementById('whatsappChatPopup');
        const messageForm = document.getElementById('whatsappMessageForm');
        const messageInput = document.getElementById('whatsappMessage');
        const messageContainer = document.querySelector('.message-container');
        
        // WhatsApp number from database
        const whatsappNumber = '<?= $primary_whatsapp_clean ?>';
        
        // Toggle chat popup
        openBtn.addEventListener('click', function() {
            popup.style.display = 'block';
        });
        
        closeBtn.addEventListener('click', function() {
            popup.style.display = 'none';
        });
        
        // Handle message sending
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (message) {
                // Add sent message to chat
                const sentMsg = document.createElement('div');
                sentMsg.className = 'sent-message';
                sentMsg.innerHTML = `
                    <p>${message}</p>
                    <small class="message-time">Just now</small>
                `;
                messageContainer.appendChild(sentMsg);
                
                // Clear input
                messageInput.value = '';
                
                // Scroll to bottom
                messageContainer.scrollTop = messageContainer.scrollHeight;
                
                // Open WhatsApp in new tab after a brief delay
                setTimeout(() => {
                    const encodedMessage = encodeURIComponent(message);
                    window.open(`https://wa.me/${whatsappNumber}?text=${encodedMessage}`, '_blank');
                    
                    // Hide popup after sending
                    popup.style.display = 'none';
                }, 500);
            }
        });
    });
</script>
<?php endif; ?>

<?php include('lazy-includes/footer2.php'); ?>