<?php
require "../../config.php";

// Log the callback for debugging
error_log("Payvessel Callback Received: " . file_get_contents('php://input'));

// Get the raw POST data
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    error_log("Payvessel Callback Error: Invalid JSON data");
    http_response_code(400);
    exit;
}

// Verify the signature (you should implement proper signature verification)
$signature = $_SERVER['HTTP_X_PAYVESSEL_SIGNATURE'] ?? '';
// TODO: Implement signature verification

// Extract payment details
$reference = $data['reference'] ?? '';
$status = $data['status'] ?? '';
$amount = $data['amount'] ?? 0;

if (empty($reference)) {
    error_log("Payvessel Callback Error: Missing reference");
    http_response_code(400);
    exit;
}

try {
    // Find the transaction in database
    $query = "SELECT * FROM transactions WHERE reference = ? AND payment_method = 'payvessel'";
    $stmt = mysqli_prepare($con, $query);
    mysqli_stmt_bind_param($stmt, 's', $reference);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (!$result || mysqli_num_rows($result) == 0) {
        error_log("Payvessel Callback Error: Transaction not found for reference: $reference");
        http_response_code(404);
        exit;
    }
    
    $transaction = mysqli_fetch_assoc($result);
    
    // Update transaction status
    $new_status = ($status === 'success') ? 'completed' : 'failed';
    $update_query = "UPDATE transactions SET status = ?, updated_at = NOW() WHERE reference = ?";
    $stmt = mysqli_prepare($con, $update_query);
    mysqli_stmt_bind_param($stmt, 'ss', $new_status, $reference);
    mysqli_stmt_execute($stmt);
    
    // If payment was successful, credit the user's wallet
    if ($status === 'success') {
        $user_id = $transaction['user_id'];
        $amount_to_credit = $transaction['amount']; // Original amount without charge
        
        // Credit user's wallet
        $credit_query = "UPDATE users SET bal = bal + ? WHERE id = ?";
        $stmt = mysqli_prepare($con, $credit_query);
        mysqli_stmt_bind_param($stmt, 'di', $amount_to_credit, $user_id);
        mysqli_stmt_execute($stmt);
        
        error_log("Payvessel Payment Success: User $user_id credited with $amount_to_credit");
    }
    
    // Return success response
    http_response_code(200);
    echo json_encode(['status' => 'success']);
    
} catch (Exception $e) {
    error_log("Payvessel Callback Error: " . $e->getMessage());
    http_response_code(500);
    exit;
}
?> 