<?php
// Payvessel Webhook Handler - Following official documentation
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the raw POST data
    $payload = file_get_contents('php://input');
    $payvessel_signature = $_SERVER['HTTP_PAYVESSEL_HTTP_SIGNATURE'] ?? '';
    $ip_address = $_SERVER['REMOTE_ADDR'];
    
    // Get Payvessel secret from database
    include 'conn.php';
    $config = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM config"));
    $payvesselConfig = json_decode($config['payvessel'], true);
    $secret = $payvesselConfig[1] ?? "PVSECRET-";
    
    // Generate hash
    $hashkey = hash_hmac('sha512', $payload, $secret);
    $ipAddress = ["***********", "**************"];
    
    if ($payvessel_signature == $hashkey && in_array($ip_address, $ipAddress)) {
        $data = json_decode($payload, true);
        
        if ($data) {
            $amount = floatval($data['order']['amount']);
            $settlementAmount = floatval($data['order']['settlement_amount']);
            $fee = floatval($data['order']['fee']);
            $reference = $data['transaction']['reference'];
            $description = $data['order']['description'];
            $email = $data['customer']['email'] ?? '';
            $virtualAccountNumber = $data['virtualAccount']['virtualAccountNumber'] ?? '';
            $senderName = $data['sender']['senderName'] ?? '';
            $senderBankName = $data['sender']['senderBankName'] ?? '';
            
            // Check if reference already exists in transactions table
            $stmt = $con->prepare("SELECT ref FROM transactions WHERE ref = ?");
            $stmt->bind_param("s", $reference);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                // Transaction already exists
                echo json_encode(["message" => "transaction already exist"]);
                http_response_code(200);
            } else {
                // Process new transaction
                if (!empty($email)) {
                    // Get user details
                    $stmt = $con->prepare("SELECT bal, username FROM users WHERE email = ?");
                    $stmt->bind_param("s", $email);
                    $stmt->execute();
                    $userResult = $stmt->get_result();
                    
                    if ($userResult && $userResult->num_rows > 0) {
                        $userData = $userResult->fetch_assoc();
                        $oldBalance = $userData['bal'];
                        $newBalance = $oldBalance + $settlementAmount;
                        $username = $userData['username'];
                        
                        // Update user balance
                        $stmt = $con->prepare("UPDATE users SET bal = ? WHERE email = ?");
                        $stmt->bind_param("ds", $newBalance, $email);
                        $updateResult = $stmt->execute();
                        
                        if ($updateResult) {
                            // Record transaction
                            $currentDate = date('d-m-Y h:i:s a', time());
                            $transactionType = 'Wallet Funding';
                            $transactionStatus = 'success';
                            $transactionDesc = "Wallet funded via Payvessel Virtual Account ($virtualAccountNumber) - From: $senderName ($senderBankName)";
                            
                            $stmt = $con->prepare("INSERT INTO transactions (ref, type, status, amount, oldBal, newBal, email, date_time, t_desc) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                            $stmt->bind_param("sssddsss", $reference, $transactionType, $transactionStatus, $settlementAmount, $oldBalance, $newBalance, $email, $currentDate, $transactionDesc);
                            $stmt->execute();
                        }
                    }
                }
                
                // Return success
                echo json_encode(["message" => "success"]);
                http_response_code(200);
            }
        } else {
            echo json_encode(["message" => "Invalid payload"]);
            http_response_code(400);
        }
    } else {
        echo json_encode(["message" => "Permission denied, invalid hash or ip address."]);
        http_response_code(400);
    }
} else {
    // Handle other HTTP methods
    echo json_encode(["message" => "Method not allowed"]);
    http_response_code(405);
}
?> 