<?php
// Test Script for Virtual Account Creation
// This script helps test the virtual account creation process

session_start();
include 'conn.php';

echo "<h2>Virtual Account Test Script</h2>";

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo "<p style='color: red;'>❌ User not logged in</p>";
    echo "<p>Please login first to test virtual accounts.</p>";
    exit();
}

$username = $_SESSION['username'];
$data = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM users WHERE username = '$username'"));

echo "<h3>User Information:</h3>";
echo "<p><strong>Username:</strong> " . htmlspecialchars($username) . "</p>";
echo "<p><strong>Email:</strong> " . htmlspecialchars($data['email']) . "</p>";
echo "<p><strong>Name:</strong> " . htmlspecialchars($data['name']) . "</p>";
echo "<p><strong>Phone:</strong> " . htmlspecialchars($data['phone']) . "</p>";

// Check KYC Status
echo "<h3>KYC Status:</h3>";
if (!empty($data['bvn']) && !empty($data['nin'])) {
    echo "<p style='color: green;'>✅ KYC Completed</p>";
    echo "<p><strong>BVN:</strong> " . htmlspecialchars($data['bvn']) . "</p>";
    echo "<p><strong>NIN:</strong> " . htmlspecialchars($data['nin']) . "</p>";
} else {
    echo "<p style='color: red;'>❌ KYC Not Completed</p>";
    echo "<p>BVN: " . (empty($data['bvn']) ? 'Not provided' : 'Provided') . "</p>";
    echo "<p>NIN: " . (empty($data['nin']) ? 'Not provided' : 'Provided') . "</p>";
    echo "<p><strong>Note:</strong> KYC completion is required for virtual accounts.</p>";
    exit();
}

// Check existing virtual accounts
echo "<h3>Existing Virtual Accounts:</h3>";
$existingAccounts = json_decode($data['payvessel_accounts'] ?? '[]', true);

if (empty($existingAccounts)) {
    echo "<p style='color: orange;'>⚠️ No virtual accounts found</p>";
    echo "<p>Click the button below to create virtual accounts:</p>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='create_accounts' class='btn btn-primary'>Create Virtual Accounts</button>";
    echo "</form>";
} else {
    echo "<p style='color: green;'>✅ Virtual accounts already exist</p>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    foreach ($existingAccounts as $account) {
        echo "<div style='margin-bottom: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 3px;'>";
        echo "<strong>Bank:</strong> " . htmlspecialchars($account['bankName']) . "<br>";
        echo "<strong>Account Number:</strong> <code>" . htmlspecialchars($account['accountNumber']) . "</code><br>";
        echo "<strong>Account Name:</strong> " . htmlspecialchars($account['accountName']) . "<br>";
        echo "<strong>Account Type:</strong> " . htmlspecialchars($account['account_type']) . "<br>";
        if (isset($account['expire_date'])) {
            echo "<strong>Expires:</strong> " . htmlspecialchars($account['expire_date']) . "<br>";
        }
        echo "</div>";
    }
    echo "</div>";
}

// Handle account creation
if (isset($_POST['create_accounts'])) {
    echo "<h3>Creating Virtual Accounts...</h3>";
    
    // Get Payvessel configuration
    $config = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM config"));
    $payvesselConfig = json_decode($config['payvessel'], true);
    $apiKey = $payvesselConfig[0] ?? '';
    $apiSecret = $payvesselConfig[1] ?? '';

    if (empty($apiKey) || empty($apiSecret)) {
        echo "<p style='color: red;'>❌ Payvessel not configured in admin panel</p>";
        exit();
    }

    // Prepare request data
    $requestData = [
        'email' => $data['email'],
        'name' => $data['name'],
        'phoneNumber' => $data['phone'],
        'bankcode' => ['999991', '120001'], // PalmPay and 9Payment Service Bank
        'account_type' => 'STATIC',
        'businessid' => $config['site_name'] ?? 'DinaPlug',
        'bvn' => $data['bvn'],
        'nin' => $data['nin']
    ];

    echo "<p><strong>Request Data:</strong></p>";
    echo "<pre>" . json_encode($requestData, JSON_PRETTY_PRINT) . "</pre>";

    // Make API request
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://api.payvessel.com/pms/api/external/request/customerReservedAccount/',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => json_encode($requestData),
        CURLOPT_HTTPHEADER => array(
            'api-key: ' . $apiKey,
            'api-secret: Bearer ' . $apiSecret,
            'Content-Type: application/json'
        ),
    ));

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $err = curl_error($curl);
    curl_close($curl);

    echo "<p><strong>API Response (HTTP $httpCode):</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";

    if ($err) {
        echo "<p style='color: red;'>❌ Network Error: $err</p>";
    } else {
        $result = json_decode($response, true);
        
        if ($httpCode === 200 || $httpCode === 201) {
            if (isset($result['status']) && $result['status'] === true && isset($result['banks'])) {
                // Save to database
                $accountsJson = json_encode($result['banks']);
                $stmt = $con->prepare("UPDATE users SET payvessel_accounts = ? WHERE username = ?");
                $stmt->bind_param("ss", $accountsJson, $username);

                if ($stmt->execute()) {
                    echo "<p style='color: green;'>✅ Virtual accounts created and saved successfully!</p>";
                    echo "<p><strong>Created Accounts:</strong></p>";
                    foreach ($result['banks'] as $account) {
                        echo "<div style='margin-bottom: 10px; padding: 10px; border: 1px solid #28a745; border-radius: 3px; background: #d4edda;'>";
                        echo "<strong>Bank:</strong> " . htmlspecialchars($account['bankName']) . "<br>";
                        echo "<strong>Account Number:</strong> <code>" . htmlspecialchars($account['accountNumber']) . "</code><br>";
                        echo "<strong>Account Name:</strong> " . htmlspecialchars($account['accountName']) . "<br>";
                        echo "<strong>Account Type:</strong> " . htmlspecialchars($account['account_type']) . "<br>";
                        if (isset($account['expire_date'])) {
                            echo "<strong>Expires:</strong> " . htmlspecialchars($account['expire_date']) . "<br>";
                        }
                        echo "</div>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Failed to save accounts to database</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Invalid response from Payvessel</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ API Error: HTTP $httpCode</p>";
        }
    }
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Transfer money to any of the virtual account numbers</li>";
echo "<li>Payment will be automatically credited to your wallet</li>";
echo "<li>Check your transaction history for confirmation</li>";
echo "</ol>";

echo "<p><a href='web/app-/payvessel_virtual_account.php' class='btn btn-secondary'>Go to Virtual Account Page</a></p>";
?> 