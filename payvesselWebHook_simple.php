<?php
// Simplified webhook for testing
header('Content-Type: application/json');

// Log the request
error_log("Payvessel Webhook Simple - Received at: " . date('Y-m-d H:i:s'));

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("Payvessel Webhook Simple - Not POST request");
    http_response_code(405);
    echo json_encode(["message" => "Method not allowed"]);
    exit;
}

try {
    // Get the raw POST data
    $payload = file_get_contents('php://input');
    error_log("Payvessel Webhook Simple - Payload: " . $payload);
    
    // Parse the JSON
    $data = json_decode($payload, true);
    
    if (!$data) {
        error_log("Payvessel Webhook Simple - Invalid JSON");
        http_response_code(400);
        echo json_encode(["message" => "Invalid JSON"]);
        exit;
    }
    
    // Extract basic information
    $reference = $data['transaction']['reference'] ?? '';
    $amount = $data['order']['settlement_amount'] ?? 0;
    $email = $data['customer']['email'] ?? '';
    
    error_log("Payvessel Webhook Simple - Extracted: Reference=$reference, Amount=$amount, Email=$email");
    
    // Simple success response
    $response = [
        "message" => "success",
        "reference" => $reference,
        "amount" => $amount,
        "email" => $email,
        "timestamp" => date('Y-m-d H:i:s')
    ];
    
    error_log("Payvessel Webhook Simple - Success response");
    http_response_code(200);
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Payvessel Webhook Simple - Exception: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(["message" => "Internal server error", "error" => $e->getMessage()]);
}
?> 