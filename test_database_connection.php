<?php
session_start();

echo "<h2>Database Connection Test</h2>";

// Test 1: Basic connection
echo "<h3>1. Basic Connection Test:</h3>";
try {
    include 'conn.php';
    if (isset($con) && $con) {
        echo "<p style='color: green;'>✅ Database connection successful</p>";
    } else {
        echo "<p style='color: red;'>❌ Database connection failed</p>";
        exit();
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Connection error: " . $e->getMessage() . "</p>";
    exit();
}

// Test 2: Check if user is logged in
echo "<h3>2. Session Check:</h3>";
if (!isset($_SESSION['data']['username'])) {
    echo "<p style='color: red;'>❌ User not logged in</p>";
    echo "<p>Please login first.</p>";
    exit();
}

$username = $_SESSION['data']['username'];
echo "<p style='color: green;'>✅ User logged in: " . htmlspecialchars($username) . "</p>";

// Test 3: Check if user exists in database
echo "<h3>3. User Database Check:</h3>";
$userQuery = "SELECT id, username, email, name, bvn, nin FROM users WHERE username = '$username'";
echo "<p><strong>Query:</strong> $userQuery</p>";

$userResult = mysqli_query($con, $userQuery);
if (!$userResult) {
    echo "<p style='color: red;'>❌ Query failed: " . mysqli_error($con) . "</p>";
    exit();
}

$userData = mysqli_fetch_assoc($userResult);
if (!$userData) {
    echo "<p style='color: red;'>❌ User not found in database</p>";
    exit();
}

echo "<p style='color: green;'>✅ User found in database</p>";
echo "<p><strong>User ID:</strong> " . $userData['id'] . "</p>";
echo "<p><strong>Username:</strong> " . htmlspecialchars($userData['username']) . "</p>";
echo "<p><strong>Email:</strong> " . htmlspecialchars($userData['email']) . "</p>";
echo "<p><strong>Name:</strong> " . htmlspecialchars($userData['name']) . "</p>";

// Test 4: Check KYC data
echo "<h3>4. KYC Data Check:</h3>";
echo "<p><strong>BVN:</strong> " . (empty($userData['bvn']) ? 'NULL/Empty' : htmlspecialchars($userData['bvn'])) . "</p>";
echo "<p><strong>NIN:</strong> " . (empty($userData['nin']) ? 'NULL/Empty' : htmlspecialchars($userData['nin'])) . "</p>";

// Test 5: Test database update
echo "<h3>5. Database Update Test:</h3>";
if (isset($_POST['test_update'])) {
    $testBvn = $_POST['test_bvn'];
    $testNin = $_POST['test_nin'];
    
    $updateQuery = "UPDATE users SET bvn = '$testBvn', nin = '$testNin' WHERE username = '$username'";
    echo "<p><strong>Update Query:</strong> $updateQuery</p>";
    
    if (mysqli_query($con, $updateQuery)) {
        echo "<p style='color: green;'>✅ Update successful</p>";
        
        // Verify the update
        $verifyQuery = "SELECT bvn, nin FROM users WHERE username = '$username'";
        $verifyResult = mysqli_query($con, $verifyQuery);
        $verifyData = mysqli_fetch_assoc($verifyResult);
        
        echo "<p><strong>After Update:</strong></p>";
        echo "<p>BVN: " . (empty($verifyData['bvn']) ? 'NULL/Empty' : $verifyData['bvn']) . "</p>";
        echo "<p>NIN: " . (empty($verifyData['nin']) ? 'NULL/Empty' : $verifyData['nin']) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Update failed: " . mysqli_error($con) . "</p>";
    }
} else {
    echo "<form method='POST'>";
    echo "<p><strong>Test Database Update:</strong></p>";
    echo "<p>BVN: <input type='text' name='test_bvn' placeholder='11 digits' maxlength='11'></p>";
    echo "<p>NIN: <input type='text' name='test_nin' placeholder='11 digits' maxlength='11'></p>";
    echo "<p><input type='submit' name='test_update' value='Test Update'></p>";
    echo "</form>";
}

// Test 6: Check all users with KYC data
echo "<h3>6. All Users with KYC Data:</h3>";
$allUsersQuery = "SELECT username, bvn, nin FROM users WHERE bvn IS NOT NULL OR nin IS NOT NULL LIMIT 10";
$allUsersResult = mysqli_query($con, $allUsersQuery);

if ($allUsersResult) {
    echo "<p>Users with KYC data:</p>";
    while ($row = mysqli_fetch_assoc($allUsersResult)) {
        echo "<p>Username: " . htmlspecialchars($row['username']) . 
             " | BVN: " . (empty($row['bvn']) ? 'NULL' : $row['bvn']) . 
             " | NIN: " . (empty($row['nin']) ? 'NULL' : $row['nin']) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Query failed: " . mysqli_error($con) . "</p>";
}

echo "<hr>";
echo "<h3>Summary:</h3>";
echo "<p>If all tests pass, the database is working correctly.</p>";
echo "<p><a href='investigate_kyc_issue.php'>Investigate KYC Issue</a></p>";
echo "<p><a href='test_payvessel_api.php'>Test Payvessel API</a></p>";

mysqli_close($con);
?> 