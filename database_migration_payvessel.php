<?php
// Database Migration Script for Payvessel Integration
// Run this script once to add required columns

include 'conn.php';

echo "<h2>Payvessel Database Migration</h2>";
echo "<p>Adding required columns for Payvessel integration...</p>";

$success = true;
$errors = [];

// 1. Add payvessel column to config table
try {
    $sql = "ALTER TABLE config ADD COLUMN payvessel TEXT DEFAULT NULL";
    if (mysqli_query($con, $sql)) {
        echo "<p style='color: green;'>✅ Added 'payvessel' column to config table</p>";
    } else {
        $errors[] = "Failed to add payvessel column to config table: " . mysqli_error($con);
        $success = false;
    }
} catch (Exception $e) {
    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
        echo "<p style='color: orange;'>⚠️ 'payvessel' column already exists in config table</p>";
    } else {
        $errors[] = "Error adding payvessel column: " . $e->getMessage();
        $success = false;
    }
}

// 2. Add payvessel_accounts column to users table
try {
    $sql = "ALTER TABLE users ADD COLUMN payvessel_accounts TEXT DEFAULT NULL";
    if (mysqli_query($con, $sql)) {
        echo "<p style='color: green;'>✅ Added 'payvessel_accounts' column to users table</p>";
    } else {
        $errors[] = "Failed to add payvessel_accounts column to users table: " . mysqli_error($con);
        $success = false;
    }
} catch (Exception $e) {
    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
        echo "<p style='color: orange;'>⚠️ 'payvessel_accounts' column already exists in users table</p>";
    } else {
        $errors[] = "Error adding payvessel_accounts column: " . $e->getMessage();
        $success = false;
    }
}

// 3. Check if columns exist and show status
echo "<h3>Database Status:</h3>";

// Check config table
$result = mysqli_query($con, "DESCRIBE config");
$configColumns = [];
while ($row = mysqli_fetch_assoc($result)) {
    $configColumns[] = $row['Field'];
}

if (in_array('payvessel', $configColumns)) {
    echo "<p style='color: green;'>✅ 'payvessel' column exists in config table</p>";
} else {
    echo "<p style='color: red;'>❌ 'payvessel' column missing from config table</p>";
    $success = false;
}

// Check users table
$result = mysqli_query($con, "DESCRIBE users");
$userColumns = [];
while ($row = mysqli_fetch_assoc($result)) {
    $userColumns[] = $row['Field'];
}

if (in_array('payvessel_accounts', $userColumns)) {
    echo "<p style='color: green;'>✅ 'payvessel_accounts' column exists in users table</p>";
} else {
    echo "<p style='color: red;'>❌ 'payvessel_accounts' column missing from users table</p>";
    $success = false;
}

// Show any errors
if (!empty($errors)) {
    echo "<h3>Errors:</h3>";
    foreach ($errors as $error) {
        echo "<p style='color: red;'>❌ $error</p>";
    }
}

// Final status
if ($success) {
    echo "<h3 style='color: green;'>🎉 Migration completed successfully!</h3>";
    echo "<p>You can now configure Payvessel in the admin panel.</p>";
} else {
    echo "<h3 style='color: red;'>⚠️ Migration completed with errors</h3>";
    echo "<p>Please check the errors above and run the migration again if needed.</p>";
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Configure Payvessel credentials in admin panel</li>";
echo "<li>Set up webhook URL in Payvessel dashboard</li>";
echo "<li>Test the integration with small amounts</li>";
echo "</ol>";

mysqli_close($con);
?> 