<?php 
$actual_link = $_SERVER['REMOTE_ADDR'];
//if($actual_link != '198.187.31.83') die('Invalid REQUEST');
include "../../conn.php";

if ($_SERVER["REQUEST_METHOD"] == "GET") {
	die(json_encode(array('error' => true, 'desc' => 'Unsupported Method')));
}
$headers = apache_request_headers();
if (!isset($headers['Authorization']) || empty($headers['Authorization'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token1')));
}

if (!isset($_POST['meter_number']) || empty($_POST['meter_number'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid meter_number')));
}
if (!isset($_POST['service_id']) || empty($_POST['service_id'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid service_id')));
}

if (!isset($_POST['type']) || empty($_POST['type'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid type')));
}
$meter_number = mysqli_real_escape_string($con, $_POST['meter_number']);
$service_id = mysqli_real_escape_string($con, $_POST['service_id']);
$type = mysqli_real_escape_string($con, $_POST['type']);

// $req_token = mysqli_real_escape_string($con, $headers['Authorization']);

// if (!str_contains($req_token, 'Token')) {
// 	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token2')));
// }

// if (!isset(explode(' ', $req_token)[1]) || empty(explode(' ', $req_token)[1])) {
// 	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token1')));
// }

// $token = explode(' ', $req_token)[1];


$netsToken = mysqli_query($con, "SELECT * FROM apikey WHERE platform='vtpass' AND types='airtime' ");
$tokenKey = mysqli_fetch_assoc($netsToken);
if (!$tokenKey) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid apikey')));
}
$apiLink = "https://vtpass.com/api/merchant-verify";//$tokenKey['apiLink'];
$apiKey = $tokenKey['apiKey'];
$secretkey = $tokenKey['secretkey'];


$curl = curl_init();
$payload = ['api-key: ' . $apiKey, 'secret-key: ' . $secretkey];
curl_setopt_array($curl, array(
	CURLOPT_URL => $apiLink,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_ENCODING => '',
	CURLOPT_MAXREDIRS => 10,
	CURLOPT_TIMEOUT => 0,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	CURLOPT_CUSTOMREQUEST => 'POST',
	CURLOPT_POSTFIELDS => array('billersCode' => $meter_number,'serviceID' => $service_id,'type' => $type),
	CURLOPT_HTTPHEADER => $payload,
));

$response = curl_exec($curl);
file_put_contents('aaa.txt', $response);
curl_close($curl);

$resp = json_decode($response);


if ($resp->code == '000') {
	if (isset($resp->content->error)) {
		die(json_encode(array('error' => true, 'status' => 400, 'desc' => $resp->content->error)));
	}else{
		die(json_encode(array('error' => false, 'status' => 200, 'name' => $resp->content->Customer_Name, 'address' => $resp->content->Address)));
	}
}else{
	die(json_encode(array('error' => true, 'status' => 407, 'desc' => $resp->response_description)));
}
