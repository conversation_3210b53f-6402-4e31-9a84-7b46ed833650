<?php
include "../../config.php";

// Enable error reporting for debugging
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);
// error_reporting(0); // REMOVED: This was hiding errors

// Check DB connection immediately after include
if (!$con) {
  die("Database connection failed immediately after include in sign_up: " . mysqli_connect_error());
}

$refUsername = '';
if (isset($_GET['referrer'])) {
  // Remove deprecated filter
  $refU = strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_GET['referrer']))));
  $checkSql = mysqli_query($con, "SELECT username FROM users WHERE username = '$refU'");
  if (!$checkSql) { die("Error checking referrer: " . mysqli_error($con)); }
  if (mysqli_num_rows($checkSql) == 1) {
    $userDet = mysqli_fetch_assoc($checkSql);
    $refUsername = $userDet['username'];
  } else {
    echo "<script>alert('INVALID REFERAL ID')</script>";
  }
}

if (isset($_POST['reg'])) {
  // Remove deprecated filters
  $name = trim(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['name'])))));
  $email = trim(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['email'])))));
  $username = trim(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['username'])))));
  $phone = trim(strip_tags(mysqli_real_escape_string($con, trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['phone'])))));
  
  // Check if 'refer' is set before accessing it
  $refer_input = isset($_POST['refer']) ? $_POST['refer'] : ''; // Default to empty string if not set
  $refer = trim(strip_tags(trim(preg_replace('/[\t\n\r\s]+/', ' ', $refer_input))));
  
  // Remove escape from passwords before hashing
  $pass = trim(strip_tags(trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['pass']))));
  $repeat_pass = trim(strip_tags(trim(preg_replace('/[\t\n\r\s]+/', ' ', $_POST['repeat_pass']))));

  if (empty($name)) {
    $_SESSION['reg_msg'] = 'Full Name is Required';
    goto end;
  } elseif (empty($email)) {
    $_SESSION['reg_msg'] = 'Email is Required';
    goto end;
  } elseif (empty($username)) {
    $_SESSION['reg_msg'] = 'Username is Required';
    goto end;
  } elseif (str_word_count($name) < 2) {
    $_SESSION['reg_msg'] = 'Name must include last-name and first-name';
    goto end;
  } elseif (empty($phone)) {
    $_SESSION['reg_msg'] = 'Phone Number is Required';
    goto end;
  } elseif (strlen($phone) != 11) {
    $_SESSION['reg_msg'] = 'Invalid Phone Number';
    goto end;
  } elseif (empty($pass)) {
    $_SESSION['reg_msg'] = 'Password is Required';
    goto end;
  } elseif (empty($repeat_pass)) {
    $_SESSION['reg_msg'] = 'Repeat Password is Required';
    goto end;
  } elseif ($pass != $repeat_pass) {
    $_SESSION['reg_msg'] = "Passwords aren't Matched";
    goto end;
  } elseif (strlen($pass) < 8) {
    $_SESSION['reg_msg'] = "Passwords length must be at least 8";
    goto end;
  } else {
    if (mysqli_num_rows(mysqli_query($con, "SELECT id FROM users WHERE email='$email'")) != 0) {
      $_SESSION['reg_msg'] = "Email has already been used";
      goto end;
    } elseif (mysqli_num_rows(mysqli_query($con, "SELECT id FROM users WHERE username='$username'")) != 0) {
      $_SESSION['reg_msg'] = "Username has already been used";
      goto end;
    } elseif (mysqli_num_rows(mysqli_query($con, "SELECT id FROM users WHERE phone='$phone'")) != 0) {
      $_SESSION['reg_msg'] = "Phone Number has already been used";
      goto end;
    } elseif (!empty($refer) && mysqli_num_rows(mysqli_query($con, "SELECT id FROM users WHERE username='$refer'")) == 0) {
      $_SESSION['reg_msg'] = "INVALID REFERRER ID";
      goto end;
    }
    $password = md5($pass);

    // Add check for the SELECT query before referral update
    $selectReferQuery = "SELECT refs FROM users WHERE username = '$refer'";
    $referResult = mysqli_query($con, $selectReferQuery);
    if (!$referResult) { die("Error fetching referrer details: " . mysqli_error($con)); }
    $oldUserRefs = mysqli_fetch_assoc($referResult);

    if ($oldUserRefs) { // Proceed only if referrer was found
        $refs = json_decode($oldUserRefs['refs']);
        // Check if json_decode failed or if $refs is not an array
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($refs)) {
            // Handle error: Initialize refs as an empty array or log an issue
            // For now, let's assume it should be an array, initialize if invalid
            $refs = [];
            // Optionally log: error_log("Invalid JSON in refs for user: $refer");
        }
        array_push($refs, $username);
        $newRefs = json_encode($refs);

        // Add check for the UPDATE query
        $updateReferQuery = "UPDATE users SET refs = '$newRefs' WHERE username = '$refer'";
        if (!mysqli_query($con, $updateReferQuery)) {
            die("Error updating referrer refs: " . mysqli_error($con));
        }
    }

    $token = md5($current_date . $username . 'VTU');

    // Added 'day_spent' column with default 0
    // Added 'bank_added' column with default 0
    // Re-adding common fields proactively (excluding acct_no)
    // Reverting to only include confirmed necessary columns + defaults
    // Added 'max_airtime' column with default 0
    $insertQuery = "INSERT INTO users (name, email, username, phone, refBy, regDate, password, token, refs, compRef, status, address, staff, otp, day_spent, bank_added, bank, max_airtime) VALUES ('$name', '$email', '$username', '$phone', '$refer', '$current_date', '$password', '$token', '[]', '[]', 'verified', '', '0', '0', '0', '0', '', '0')";
    if (mysqli_query($con, $insertQuery)) {
      $_SESSION['reg_s_msg'] = "Registered Successfully";
      // No need to re-fetch config here, it should be available from the include
      // $config = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM config"));
      // $adm = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM adm"));
    } else {
      // Display specific SQL error
      $_SESSION['reg_msg'] = "Database error during registration: " . mysqli_error($con);
      // Optionally: die("Database error during registration: " . mysqli_error($con)); // Use die for immediate feedback during debug
    }
  }
}
end:
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="<?= $config['site_name'] ?> We offer modern solutions for internet connection, We are here to always serve you">
  <meta name="author" content="<?= $config['site_name'] ?>">
  <title>Sign Up - <?= $config['site_name'] ?></title>
  <link rel="icon" type="image/png" href="../../favicon.png">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../assets/css/styles.css">
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
  <?php
  if (isset($_SESSION['reg_msg']) && !empty($_SESSION['reg_msg'])) {
  ?>
    <script>
      Swal.fire({
        title: "<?= $_SESSION["reg_msg"] ?>",
        icon: 'error',
        confirmButtonColor: '#4f46e5'
      }).then(() => {
        history.back()
      })
    </script>
  <?php
    $_SESSION['reg_msg'] = '';
  }
  if (isset($_SESSION['reg_s_msg']) && !empty($_SESSION['reg_s_msg'])) {
  ?>
    <script>
      Swal.fire({
        title: "<?= $_SESSION["reg_s_msg"] ?>",
        text: 'Press OK button and I will take you to the login page!',
        icon: 'success',
        confirmButtonColor: '#4f46e5'
      }).then(() => {
        window.location.replace("../login");
      })
    </script>
  <?php
    $_SESSION['reg_s_msg'] = '';
  }
  ?>

  <div class="container">
    <!-- Header -->
    <header>
      <div class="text-center mb-4">
        <a href="https://dinaplug.com/" class="logo">
          <!-- <i class="fas fa-bolt icon"></i> Optional: Add an icon if desired -->
           <span style="color: var(--primary); font-weight: 700; font-size: 1.25rem;">Dinaplug</span> <!-- Text logo using primary color -->
        </a>
      </div>
      <nav class="desktop-nav">
        <a href="../login">Login</a>
        <a href="../sign_up">Sign Up</a>
      </nav>
      <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
      </button>
    </header>
    
    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
      <nav>
        <a href="../login">Login</a>
        <a href="../sign_up">Sign Up</a>
      </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
      <div class="page-header">
        <h1>Create Account</h1>
        <p>Join us and start your journey today</p>
      </div>
      
      <div class="card">
        <form method="POST">
          <!-- Referrer Username -->
       

          <!-- Full Name -->
          <div class="form-group">
            <label for="name">Full Name</label>
            <div class="input-wrapper">
              <i class="fas fa-user icon"></i>
              <input 
                type="text" 
                id="name" 
                name="name" 
                placeholder="Enter your full name" 
                required
              >
            </div>
          </div>

          <!-- Email Address -->
          <div class="form-group">
            <label for="email">Email Address</label>
            <div class="input-wrapper">
              <i class="fas fa-envelope icon"></i>
              <input 
                type="email" 
                id="email" 
                name="email" 
                placeholder="Enter your email" 
                required
              >
            </div>
          </div>

          <!-- Username -->
          <div class="form-group">
            <label for="username">Username</label>
            <div class="input-wrapper">
              <i class="fas fa-at icon"></i>
              <input 
                type="text" 
                id="username" 
                name="username" 
                placeholder="Choose a username" 
                required
              >
            </div>
          </div>

          <!-- Phone Number -->
          <div class="form-group">
            <label for="phone">Phone Number</label>
            <div class="input-wrapper">
              <i class="fas fa-phone icon"></i>
              <input 
                type="tel" 
                id="phone" 
                name="phone" 
                placeholder="Enter your phone number" 
                maxlength="11"
                required
              >
            </div>
          </div>

          <!-- Password -->
          <div class="form-group">
            <label for="password">Password</label>
            <div class="input-wrapper">
              <i class="fas fa-lock icon"></i>
              <input 
                type="password" 
                id="password" 
                name="pass" 
                placeholder="Create a strong password" 
                required
              >
              <button 
                type="button" 
                id="togglePassword" 
                class="toggle-password"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <div class="password-strength">
              <div id="passwordStrength" class="password-strength-bar"></div>
            </div>
            <p class="password-hint">
              Password must be at least 8 characters long and include uppercase, lowercase, numbers, and special characters.
            </p>
          </div>

          <!-- Confirm Password -->
          <div class="form-group">
            <label for="repeat_password">Confirm Password</label>
            <div class="input-wrapper">
              <i class="fas fa-lock icon"></i>
              <input 
                type="password" 
                id="repeat_password" 
                name="repeat_pass" 
                placeholder="Confirm your password" 
                required
              >
              <button 
                type="button" 
                id="toggleRepeatPassword" 
                class="toggle-password"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>

          <!-- Terms and Conditions -->
          <div class="checkbox-group">
            <input 
              type="checkbox" 
              id="terms" 
              name="terms"
              required
              checked
            >
            <label for="terms">
              I agree to the <a href="javascript:;">Terms and Conditions</a>
            </label>
          </div>

          <!-- Submit Button -->
          <button type="submit" name="reg">
            Create Account
          </button>

          <!-- Login Link -->
          <div class="form-footer">
            <span>Already have an account?</span>
            <a href="../login">Sign in</a>
          </div>
        </form>
      </div>
      
      <!-- Footer -->
      <footer>
        &copy; <?= date('Y') ?> <?=$config['site_name'] ?>. All rights reserved.
      </footer>
    </div>
  </div>

  <script>
    // Password strength meter
    const togglePassword = document.querySelector('#togglePassword');
    const toggleRepeatPassword = document.querySelector('#toggleRepeatPassword');
    const password = document.querySelector('#password');
    const repeatPassword = document.querySelector('#repeat_password');
    const passwordStrength = document.querySelector('#passwordStrength');

    function updatePasswordStrength(password) {
      let strength = 0;
      
      if (password.length >= 8) strength += 1;
      if (password.match(/[a-z]/)) strength += 1;
      if (password.match(/[A-Z]/)) strength += 1;
      if (password.match(/[0-9]/)) strength += 1;
      if (password.match(/[^a-zA-Z0-9]/)) strength += 1;
      
      passwordStrength.className = 'password-strength-bar';
      
      if (strength <= 2) {
        passwordStrength.classList.add('weak');
        passwordStrength.classList.remove('medium', 'strong');
      } else if (strength <= 4) {
        passwordStrength.classList.add('medium');
        passwordStrength.classList.remove('weak', 'strong');
      } else {
        passwordStrength.classList.add('strong');
        passwordStrength.classList.remove('weak', 'medium');
      }
    }

    password.addEventListener('input', function() {
      updatePasswordStrength(this.value);
    });

    togglePassword.addEventListener('click', function() {
      const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
      password.setAttribute('type', type);
      this.querySelector('i').classList.toggle('fa-eye');
      this.querySelector('i').classList.toggle('fa-eye-slash');
    });

    toggleRepeatPassword.addEventListener('click', function() {
      const type = repeatPassword.getAttribute('type') === 'password' ? 'text' : 'password';
      repeatPassword.setAttribute('type', type);
      this.querySelector('i').classList.toggle('fa-eye');
      this.querySelector('i').classList.toggle('fa-eye-slash');
    });
    
    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');

    mobileMenuBtn.addEventListener('click', function() {
      mobileMenu.classList.toggle('active');
    });
  </script>
</body>
</html>

