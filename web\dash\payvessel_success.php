<?php
include 'header.php';

$reference = $_GET['reference'] ?? '';
$status = $_GET['status'] ?? '';

// If no reference provided, redirect to dashboard
if (empty($reference)) {
    header('Location: index');
    exit;
}
?>

<div class="container-fluid mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-6">
      <div class="card">
        <div class="card-body text-center">
          
          <?php if ($status === 'success'): ?>
            <div class="mb-4">
              <i class="fa fa-check-circle text-success" style="font-size: 4rem;"></i>
            </div>
            <h3 class="text-success">Payment Successful!</h3>
            <p class="text-muted">Your wallet has been funded successfully.</p>
            <p><strong>Reference:</strong> <?= htmlspecialchars($reference) ?></p>
            
            <div class="mt-4">
              <a href="transactions" class="btn btn-primary">
                <i class="fa fa-list"></i> View Transaction History
              </a>
              <a href="index" class="btn btn-secondary">
                <i class="fa fa-home"></i> Go to Dashboard
              </a>
            </div>
            
          <?php else: ?>
            <div class="mb-4">
              <i class="fa fa-times-circle text-danger" style="font-size: 4rem;"></i>
            </div>
            <h3 class="text-danger">Payment Failed</h3>
            <p class="text-muted">Your payment was not completed successfully.</p>
            <p><strong>Reference:</strong> <?= htmlspecialchars($reference) ?></p>
            
            <div class="mt-4">
              <a href="payvessel" class="btn btn-primary">
                <i class="fa fa-credit-card"></i> Try Again
              </a>
              <a href="index" class="btn btn-secondary">
                <i class="fa fa-home"></i> Go to Dashboard
              </a>
            </div>
            
          <?php endif; ?>
          
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Auto-refresh wallet balance after successful payment
<?php if ($status === 'success'): ?>
setTimeout(function() {
    location.reload();
}, 3000);
<?php endif; ?>
</script> 