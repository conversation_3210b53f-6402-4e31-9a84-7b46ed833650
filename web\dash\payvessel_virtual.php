<?php
include 'header.php';

// Check if Payvessel is configured
$payvessel_config = json_decode($config['payvessel'], true);
$payvessel_enabled = !empty($payvessel_config) && is_array($payvessel_config) && count($payvessel_config) >= 2 && !empty($payvessel_config[0]) && !empty($payvessel_config[1]);

// Check user's KYC status
$kyc_done = !empty($data['BVN']) || !empty($data['NIN']);
$payvessel_accounts = !empty($data['payvessel_accounts']) ? json_decode($data['payvessel_accounts'], true) : null;
?>

<div class="container-fluid mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Payvessel Virtual Account</h3>
          <p class="card-description">Get dedicated bank account numbers for instant wallet funding</p>
        </div>
        <div class="card-body">
          
          <?php if (!$payvessel_enabled): ?>
            <div class="alert alert-warning">
              <i class="fa fa-exclamation-triangle"></i>
              <strong>Payvessel is not configured!</strong> Please contact the administrator to set up Payvessel payment gateway.
            </div>
          <?php elseif (!$kyc_done): ?>
            <div class="alert alert-info">
              <i class="fa fa-info-circle"></i>
              <strong>KYC Verification Required</strong><br>
              To generate virtual account numbers, you need to complete your KYC verification by providing your BVN or NIN.
              <br><br>
              <a href="payvessel_kyc.php" class="btn btn-primary">
                <i class="fa fa-user-check"></i> Complete KYC Verification
              </a>
            </div>
          <?php else: ?>
            
            <?php if ($payvessel_accounts): ?>
              <!-- Display existing accounts -->
              <div class="row">
                <div class="col-12">
                  <h5 class="text-success mb-3">
                    <i class="fa fa-check-circle"></i> Your Virtual Account Numbers
                  </h5>
                  <p class="text-muted">Transfer any amount to these account numbers and your wallet will be funded instantly!</p>
                </div>
              </div>
              
              <div class="row">
                <?php foreach ($payvessel_accounts as $account): ?>
                  <div class="col-md-6 mb-3">
                    <div class="card border-success">
                      <div class="card-body">
                                                 <div class="d-flex align-items-center mb-2">
                           <?php if (strpos(strtolower($account['bankName']), 'palm') !== false): ?>
                             <img src="../assets/img/palmpay.png" class="mr-2" alt="PalmPay" style="height: 40px; width: auto;">
                           <?php elseif (strpos(strtolower($account['bankName']), '9payment') !== false): ?>
                             <img src="../assets/img/9PSB.png" class="mr-2" alt="9Payment Service Bank" style="height: 40px; width: auto;">
                           <?php else: ?>
                             <img src="https://via.placeholder.com/40x40/6c757d/ffffff?text=B" class="mr-2" alt="Bank">
                           <?php endif; ?>
                           <h6 class="mb-0"><?= htmlspecialchars($account['bankName']) ?></h6>
                         </div>
                        
                        <div class="row">
                          <div class="col-6">
                            <small class="text-muted">Account Number</small>
                            <div class="input-group">
                              <input type="text" class="form-control" value="<?= htmlspecialchars($account['accountNumber']) ?>" readonly>
                              <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?= htmlspecialchars($account['accountNumber']) ?>')">
                                  <i class="fa fa-copy"></i>
                                </button>
                              </div>
                            </div>
                          </div>
                          <div class="col-6">
                            <small class="text-muted">Account Name</small>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($account['accountName']) ?>" readonly>
                          </div>
                        </div>
                        
                        <div class="mt-2">
                          <small class="text-muted">
                            <i class="fa fa-info-circle"></i> Charge: 1.0% of transfer amount
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                <?php endforeach; ?>
              </div>
              
            <?php else: ?>
              <!-- Generate accounts button -->
              <div class="text-center">
                <div class="alert alert-info">
                  <i class="fa fa-info-circle"></i>
                  <strong>Ready to Generate Virtual Accounts!</strong><br>
                  Your KYC verification is complete. Click the button below to generate your dedicated account numbers.
                </div>
                
                <button type="button" class="btn btn-success btn-lg" onclick="generateAccounts()">
                  <i class="fa fa-university"></i> Generate Virtual Accounts
                </button>
              </div>
            <?php endif; ?>
            
          <?php endif; ?>
          
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(function() {
    Swal.fire({
      title: 'Copied!',
      text: 'Account number copied to clipboard',
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  }).catch(function(err) {
    console.error('Could not copy text: ', err);
    Swal.fire('Error', 'Failed to copy account number', 'error');
  });
}

function generateAccounts() {
  Swal.fire({
    title: 'Generating Virtual Accounts',
    text: 'Please wait while we create your dedicated account numbers...',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });
  
  $.ajax({
    url: 'payvessel_create_accounts.php',
    method: 'POST',
    data: {
      action: 'generate_accounts'
    },
    success: function(response) {
      try {
        const result = JSON.parse(response);
        if (result.success) {
          Swal.fire({
            title: 'Success!',
            text: 'Virtual accounts generated successfully!',
            icon: 'success'
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire('Error', result.message || 'Failed to generate accounts', 'error');
        }
      } catch (e) {
        Swal.fire('Error', 'Invalid response from server', 'error');
      }
    },
    error: function() {
      Swal.fire('Error', 'Failed to connect to server', 'error');
    }
  });
}
</script> 