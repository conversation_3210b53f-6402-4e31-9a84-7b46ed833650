
RewriteOptions inherit
Options -Indexes
RewriteEngine on
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php
php_value date.timezone Africa/Lagos

##RewriteCond %{HTTPS} !=on
##RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301,NE]

<IfModule mod_security.c>
SecFilterEngine Off
SecFilterScanPOST Off
</IfModule>

ErrorDocument 400 400
ErrorDocument 401 401
ErrorDocument 403 403
ErrorDocument 404 <h1>Page-Not-Found</h1>
ErrorDocument 503 503

<RequireAll>
    Require all granted
</RequireAll>
