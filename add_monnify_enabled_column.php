<?php
// <PERSON><PERSON>t to add monnify_enabled column to config table
include 'conn.php';

// Check if column already exists
$checkColumn = mysqli_query($con, "SHOW COLUMNS FROM config LIKE 'monnify_enabled'");

if (mysqli_num_rows($checkColumn) == 0) {
    // Column doesn't exist, add it
    $addColumn = mysqli_query($con, "ALTER TABLE config ADD COLUMN monnify_enabled VARCHAR(20) DEFAULT 'enabled'");
    
    if ($addColumn) {
        echo "✅ Successfully added 'monnify_enabled' column to config table<br>";
        echo "Default value set to 'enabled'<br>";
    } else {
        echo "❌ Error adding column: " . mysqli_error($con) . "<br>";
    }
} else {
    echo "ℹ️ Column 'monnify_enabled' already exists in config table<br>";
}

// Check current value
$result = mysqli_query($con, "SELECT monnify_enabled FROM config LIMIT 1");
if ($result && mysqli_num_rows($result) > 0) {
    $row = mysqli_fetch_assoc($result);
    echo "Current monnify_enabled value: " . ($row['monnify_enabled'] ?? 'NULL') . "<br>";
} else {
    echo "No config records found<br>";
}

echo "<br>✅ Migration completed!";
?> 