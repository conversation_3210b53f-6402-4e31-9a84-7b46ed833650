<?php

include 'header.php';
?>
<div class="" style="margin-top: 50px;" >


</div>
</div>
<div class="container-fluid">
  <style>
    table, tr, td, th {
      border: 2px dotted black;
      padding: 10px;
      font-size: 18px;
    }
  </style>
  
  <div class="row">

    <?php 
    // Check if Monnify is enabled
    $monnify_enabled = !isset($config['monnify_enabled']) || $config['monnify_enabled'] != 'disabled';
    
    // Check if user has Payvessel accounts
    $payvessel_accounts = !empty($data['payvessel_accounts']) ? json_decode($data['payvessel_accounts'], true) : null;
    $payvessel_kyc_done = !empty($data['BVN']) || !empty($data['NIN']);
    ?>
    
    <!-- Payvessel Accounts (Show when Monnify is disabled or if user has Payvessel accounts) -->
    <?php if (!$monnify_enabled || $payvessel_accounts): ?>
      <div class="col-md-6">
        <div class="card border border-success">
          <div class="card-header">
            <strong class="card-title">PAYVESSEL VIRTUAL ACCOUNTS</strong>
          </div>
          
          <?php if ($payvessel_accounts): ?>
            <?php foreach ($payvessel_accounts as $account): ?>
              <div class="card-body">
                <div class="d-flex align-items-center mb-2">
                  <?php if (strpos(strtolower($account['bankName']), 'palm') !== false): ?>
                    <img src="../assets/img/palmpay.png" class="mr-2" alt="PalmPay" style="height: 30px; width: auto;">
                  <?php elseif (strpos(strtolower($account['bankName']), '9payment') !== false): ?>
                    <img src="../assets/img/9PSB.png" class="mr-2" alt="9Payment Service Bank" style="height: 30px; width: auto;">
                  <?php else: ?>
                    <img src="https://via.placeholder.com/30x30/6c757d/ffffff?text=B" class="mr-2" alt="Bank">
                  <?php endif; ?>
                  <strong><?= htmlspecialchars($account['bankName']) ?></strong>
                </div>

                <p class="card-text">
                  Account Number: <b><?= htmlspecialchars($account['accountNumber']) ?></b>
                </p>
                <p class="card-text">
                  Account Name: <b><?= htmlspecialchars($account['accountName']) ?></b>
                </p>

                <p>
                  Charge: 1.0% of transfer amount. <br> For any amount you transfer to this account, 1.0% will be debited, and your wallet funded INSTANTLY!
                </p>
                <hr>
              </div>
            <?php endforeach ?>
          <?php elseif ($payvessel_kyc_done): ?>
            <div class="card-body text-center">
              <p class="text-muted">You haven't generated virtual accounts yet.</p>
              <a href="payvessel_virtual.php" class="btn btn-success">
                <i class="fa fa-university"></i> Generate Virtual Accounts
              </a>
            </div>
          <?php else: ?>
            <div class="card-body text-center">
              <p class="text-muted">Complete KYC verification to generate virtual accounts.</p>
              <a href="payvessel_kyc.php" class="btn btn-primary">
                <i class="fa fa-user-check"></i> Complete KYC
              </a>
            </div>
          <?php endif ?>
        </div>
      </div>
    <?php endif ?>
    
    <!-- Monnify Accounts (Show only when enabled) -->
    <?php if ($monnify_enabled && !empty($data['reserveBank'])): ?>
      <div class="col-md-6">
        <div class="card border border-primary">
          <div class="card-header">
            <strong class="card-title">MONNIFY VIRTUAL ACCOUNTS</strong>
          </div>
          
          <?php foreach (json_decode($data['reserveBank']) as $key => $value): ?>
            <div class="card-body">
              <p class="card-text">
                Account Number: <b><?=$value->accountNumber ?></b>
              </p>
              <p class="card-text">
                Account Name: <b>MONNIFY / ~ <?=$value->accountName ?></b>
              </p>
              <p class="card-text">
                Bank: <b><?=$value->bankName ?></b>
              </p>

              <p>
                Charge: ₦50. <br> For any amount you transfer to this account, ₦50 will be debited, and your wallet funded INSTANTLY!
              </p>
              <hr>
            </div>
          <?php endforeach ?>
        </div>
      </div>
    <?php endif ?>
  </div>

</div>
  <!-- <script src="https://js.paystack.co/v1/inline.js"></script> 
  <script>

    function payWithPaystack() {
      let empt = ['0', ''];
      if(!(empt.includes(document.getElementById('amount').value) || document.getElementById('amount').value < 100)){
        SlickLoader.setText("Funding...", "Please Wait");
        let amountVal = parseInt(document.getElementById('amount').value);
        let amt = amountVal + (1.5/100 * amountVal) + (amountVal > 2499 ? 100 : 0);
        console.log(amt);
        var handler = PaystackPop.setup({
          key: '<?=json_decode($_SESSION["config"]["paystack"])[0] ?>',
          email: "<?=$_SESSION['data']['email'] ?>",
          amount: amt * 100,
          currency: 'NGN',
          ref: "<?=$_SESSION['config']['site_name'] ?>"+ Math.floor(Math.random()*9999999999999999),
          callback: function(response) {
            SlickLoader.enable();
            var reference = response.reference;
            $.post('handler', {ref: reference}, (r1, r2)=>{
              console.log(r1);
              SlickLoader.disable();
              if(r1 == true){
                Swal.fire('Wallet Funded', 'Payment of ₦'+ amt + ' was successful', 'success').then(()=>{
                  location.reload();
                })
              }
            });
          },
          onClose: function() {
            alert('Transaction was not completed, window closed.');
          },
        });
        handler.openIframe();
      }else{
        Swal.fire('Invalid Amount', '', 'error')
      }
    }
  </script> -->
  <script type="text/javascript" src="https://sdk.monnify.com/plugin/monnify.js"></script>
  <!-- <button onclick="payWithMonnify()">Pay with Monnify</button> -->


  <script type="text/javascript">
    function payWithMonnify() {
      let empt = ['0', ''];
      if(!(empt.includes(document.getElementById('amount').value) || document.getElementById('amount').value < 100)){
        SlickLoader.setText("Funding...", "Please Wait");
        let amountVal = parseInt(document.getElementById('amount').value);
        let amt = amountVal + (1.5/100 * amountVal);
        let ref = "<?=$_SESSION['config']['site_name'] ?>" + Math.floor((Math.random() * 1000000000) + 1);
        MonnifySDK.initialize({
          amount: amt,
          currency: "NGN",
          reference: ref,
          customerName: "<?=$_SESSION['data']['name'] ?>",
          customerEmail: "<?=$_SESSION['data']['email'] ?>",
          apiKey: '<?=json_decode($_SESSION["config"]["monnify"])[0] ?>',
          contractCode: '<?=json_decode($_SESSION["config"]["monnify"])[1] ?>',
          paymentDescription: "Test Pay",
          isTestMode: true,
          paymentMethods: ["CARD"],
          onComplete: function(response){
            SlickLoader.enable();
            $.post('handler', {ref: ref, monif: 'mon', amount: amountVal, email: "<?=$_SESSION['data']['email'] ?>"}, (r1, r2)=>{
              console.log(r1);
              SlickLoader.disable();
              if(r1 == true){
                Swal.fire('Wallet Funded', 'Payment of ₦'+ amt + ' was successful', 'success').then(()=>{
                  location.reload();
                })
              }else {
                Swal.fire('Payment Failed')
              }
            });
          },
          onClose: function(data){
          }
        })
      }else{
        Swal.fire('Invalid Amount', '', 'error')
      }
    }

  </script>
  <?php
  include 'footer.php';
?>