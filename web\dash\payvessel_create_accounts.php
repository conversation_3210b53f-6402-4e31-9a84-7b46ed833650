<?php
require "../../config.php";

// Check authentication
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check if user has KYC completed
if (empty($data['BVN']) && empty($data['NIN'])) {
    echo json_encode(['success' => false, 'message' => 'KYC verification required. Please provide your BVN or NIN first.']);
    exit;
}

// Get Payvessel configuration
$payvessel_config = json_decode($config['payvessel'], true);
if (empty($payvessel_config) || !is_array($payvessel_config) || count($payvessel_config) < 2 || empty($payvessel_config[0]) || empty($payvessel_config[1])) {
    echo json_encode(['success' => false, 'message' => 'Payvessel is not configured']);
    exit;
}

try {
    // Check if user already has Payvessel accounts
    if (!empty($data['payvessel_accounts'])) {
        echo json_encode(['success' => false, 'message' => 'You already have virtual accounts generated']);
        exit;
    }
    
    // Prepare account creation data
    $account_data = [
        'email' => $data['email'],
        'name' => $data['name'],
        'phoneNumber' => $data['phone'],
        'bankcode' => '50515', // PalmPay
        'account_type' => 'STATIC',
        'businessid' => $payvessel_config['business_id'] ?? '252C8036327246D3BA4D6B50D408B23C',
        'bvn' => $data['BVN'] ?? '',
        'nin' => $data['NIN'] ?? ''
    ];
    
    error_log("Payvessel Account Creation Request: " . json_encode($account_data));
    
    // Make API call to Payvessel
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.payvessel.com/pms/api/external/request/customerReservedAccount/');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($account_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'api-key: ' . $payvessel_config[0],
        'api-secret: Bearer ' . $payvessel_config[1]
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    error_log("Payvessel API Response: HTTP $http_code - $response");
    
    if ($http_code !== 200) {
        echo json_encode(['success' => false, 'message' => 'Failed to create virtual accounts. Please try again.']);
        exit;
    }
    
    $result = json_decode($response, true);
    
    if (!$result || !isset($result['status']) || $result['status'] !== true) {
        echo json_encode(['success' => false, 'message' => 'Failed to create virtual accounts. Please try again.']);
        exit;
    }
    
    // Extract account details from response
    $accounts = [];
    if (isset($result['responseData']['banks']) && is_array($result['responseData']['banks'])) {
        foreach ($result['responseData']['banks'] as $bank) {
            $accounts[] = [
                'bankName' => $bank['bankName'] ?? 'Unknown Bank',
                'accountNumber' => $bank['accountNumber'] ?? '',
                'accountName' => $bank['accountName'] ?? $data['name']
            ];
        }
    }
    
    if (empty($accounts)) {
        echo json_encode(['success' => false, 'message' => 'No accounts were generated. Please try again.']);
        exit;
    }
    
    // Store accounts in database
    $accounts_json = json_encode($accounts);
    $username = $data['username'];
    
    $update_query = "UPDATE users SET payvessel_accounts = ? WHERE username = ?";
    $stmt = mysqli_prepare($con, $update_query);
    mysqli_stmt_bind_param($stmt, 'ss', $accounts_json, $username);
    
    if (!mysqli_stmt_execute($stmt)) {
        error_log("Database Error: " . mysqli_error($con));
        echo json_encode(['success' => false, 'message' => 'Failed to save accounts to database']);
        exit;
    }
    
    // Return success
    echo json_encode([
        'success' => true,
        'message' => 'Virtual accounts generated successfully!',
        'accounts' => $accounts
    ]);
    
} catch (Exception $e) {
    error_log("Payvessel Account Creation Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred. Please try again.']);
}
?> 