<?php
require "../../config.php";

// Check authentication
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get POST data
$bvn = isset($_POST['bvn']) ? trim($_POST['bvn']) : '';
$nin = isset($_POST['nin']) ? trim($_POST['nin']) : '';

// Check if at least one field is provided
if (empty($bvn) && empty($nin)) {
    echo json_encode(['success' => false, 'message' => 'Please provide either BVN or NIN (or both)']);
    exit;
}

$updates = [];
$messages = [];

// Process BVN if provided
if (!empty($bvn)) {
    if (strlen($bvn) !== 11) {
        echo json_encode(['success' => false, 'message' => 'BVN must be 11 digits']);
        exit;
    }
    if (!ctype_digit($bvn)) {
        echo json_encode(['success' => false, 'message' => 'BVN must contain only digits']);
        exit;
    }
    $updates[] = "BVN = ?";
    $messages[] = 'BVN';
}

// Process NIN if provided
if (!empty($nin)) {
    if (strlen($nin) !== 11) {
        echo json_encode(['success' => false, 'message' => 'NIN must be 11 digits']);
        exit;
    }
    if (!ctype_digit($nin)) {
        echo json_encode(['success' => false, 'message' => 'NIN must contain only digits']);
        exit;
    }
    $updates[] = "NIN = ?";
    $messages[] = 'NIN';
}

try {
    // Update user's KYC information
    $username = $data['username'];
    
    $update_query = "UPDATE users SET " . implode(', ', $updates) . " WHERE username = ?";
    $stmt = mysqli_prepare($con, $update_query);
    
    // Bind parameters
    $params = [];
    if (!empty($bvn)) $params[] = $bvn;
    if (!empty($nin)) $params[] = $nin;
    $params[] = $username;
    
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    
    if (!mysqli_stmt_execute($stmt)) {
        error_log("Database Error: " . mysqli_error($con));
        echo json_encode(['success' => false, 'message' => 'Failed to update KYC information']);
        exit;
    }
    
    // Return success
    $message = implode(' and ', $messages) . ' verification completed successfully!';
    echo json_encode([
        'success' => true,
        'message' => $message
    ]);
    
} catch (Exception $e) {
    error_log("KYC Verification Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred. Please try again.']);
}
?> 