<!-- Page content start here-->
<head>
    <style>
        .mining-container {
            padding: 20px;
            margin-bottom: 80px;
        }
        
        .mining-card {
            background: linear-gradient(135deg, #f7931a, #ff6b6b);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            padding: 25px;
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .mining-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(rgba(255,255,255,0.15), transparent 70%);
            opacity: 0.7;
        }
        
        .coin-logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 5px 15px rgba(255, 165, 0, 0.5));
        }
        
        .coin-title {
            color: #ffffff;
            font-size: 28px;
            font-weight: 800;
            text-align: center;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }
        
        .coin-subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 16px;
            text-align: center;
            margin-bottom: 25px;
            font-weight: 500;
        }
        
        .mining-stats {
            background: rgba(0,0,0,0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            color: #ffffff;
            position: relative;
        }
        
        .stat-item:not(:last-child):after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 1px;
            background: rgba(255,255,255,0.1);
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            display: flex;
            align-items: center;
        }
        
        .stat-label i {
            margin-right: 8px;
            font-size: 16px;
            color: #FFD700;
        }
        
        .stat-value {
            font-weight: 700;
            font-size: 16px;
            background: linear-gradient(90deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
        }
        
        .mining-button {
            background: rgba(0,0,0,0.3);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 50px;
            padding: 14px 25px;
            font-weight: 700;
            font-size: 16px;
            width: 100%;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .mining-button:hover {
            background: rgba(0,0,0,0.4);
            transform: translateY(-3px);
            box-shadow: 0 7px 15px rgba(0,0,0,0.2);
        }
        
        .mining-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .mining-button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: all 0.6s ease;
        }
        
        .mining-button:hover::after {
            left: 100%;
        }
        
        .coming-soon-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: #FFD700;
            padding: 8px 18px;
            border-radius: 30px;
            font-weight: 700;
            font-size: 12px;
            z-index: 10;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            animation: pulse 2s infinite;
            border: 1px solid rgba(255, 215, 0, 0.5);
            letter-spacing: 1px;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.9;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .mining-progress {
            height: 12px;
            border-radius: 6px;
            background: rgba(0,0,0,0.3);
            margin-bottom: 25px;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .mining-progress-bar {
            height: 100%;
            width: 35%;
            background: linear-gradient(90deg, #FFD700, #FFA500);
            border-radius: 6px;
            position: relative;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        .mining-progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                rgba(255,255,255,0) 0%, 
                rgba(255,255,255,0.5) 50%, 
                rgba(255,255,255,0) 100%);
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .coin-animation {
            position: relative;
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            perspective: 1000px;
        }
        
        .coin-animation .coin {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            animation: spin 10s linear infinite;
            transform-style: preserve-3d;
            filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.7));
        }
        
        @keyframes spin {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }
        
        .mining-info-card {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .mining-info-card:hover {
            transform: translateY(-5px);
        }
        
        .mining-info-title {
            color: #f7931a;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 10px;
        }
        
        .mining-info-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, #f7931a, transparent);
            border-radius: 3px;
        }
        
        .mining-info-text {
            color: rgba(255,255,255,0.9);
            font-size: 15px;
            line-height: 1.6;
        }
        
        .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateX(5px);
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #f7931a, #ff6b6b);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 20px;
            box-shadow: 0 5px 15px rgba(247, 147, 26, 0.3);
            flex-shrink: 0;
        }
        
        .feature-text {
            color: rgba(255,255,255,0.9);
            font-size: 14px;
        }
        
        .feature-text strong {
            color: #ffffff;
            font-size: 16px;
            display: block;
            margin-bottom: 5px;
        }
        
        .countdown-container {
            display: flex;
            justify-content: center;
            margin: 25px 0;
        }
        
        .countdown-item {
            background: rgba(0,0,0,0.3);
            border-radius: 12px;
            padding: 15px 10px;
            margin: 0 5px;
            min-width: 70px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .countdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background: linear-gradient(to bottom, rgba(255,255,255,0.1), transparent);
        }
        
        .countdown-value {
            font-size: 24px;
            font-weight: 800;
            color: #FFD700;
            text-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }
        
        .countdown-label {
            font-size: 12px;
            color: rgba(255,255,255,0.8);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 5px;
        }
        
        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .particle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            pointer-events: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff3b30;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
        
        .referral-link {
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 15px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .referral-text {
            color: rgba(255,255,255,0.9);
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 10px;
        }
        
        .copy-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            border-radius: 5px;
            padding: 7px 12px;
            font-size: 12px;
            white-space: nowrap;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .step-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            background: #f7931a;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            margin-right: 8px;
            font-weight: bold;
        }
        
        .glow-effect {
            animation: glow 3s infinite alternate;
        }
        
        @keyframes glow {
            0% {
                filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));
            }
            100% {
                filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
            }
        }
        
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #4CAF50;
            border-radius: 50%;
            margin-right: 5px;
            animation: blink 1.5s infinite;
        }
        
        @keyframes blink {
            0% { opacity: 0.4; }
            50% { opacity: 1; }
            100% { opacity: 0.4; }
        }
        
        .mining-card-footer {
            margin-top: 20px;
            text-align: center;
            color: rgba(255,255,255,0.7);
            font-size: 12px;
        }
        
        .mining-card-footer a {
            color: #FFD700;
            text-decoration: none;
        }
    </style>
</head>

<div class="page-content header-clear">
    <div class="mining-container">
        <div class="mining-card">
            <div class="coming-soon-badge">COMING SOON</div>
            <div class="particles-container" id="particles-container"></div>
            
            <div class="coin-animation">
                <svg class="coin glow-effect" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="45" fill="url(#coinGradient)" stroke="#FFD700" stroke-width="2" />
                    <path d="M35 40 C35 30, 65 30, 65 40 C65 48, 55 55, 50 60 C45 55, 35 48, 35 40 Z" fill="#FFD700" />
                    <circle cx="45" cy="38" r="5" fill="#000" />
                    <circle cx="55" cy="38" r="5" fill="#000" />
                    <path d="M42 50 C45 55, 55 55, 58 50" stroke="#000" stroke-width="2" fill="none" />
                    <defs>
                        <linearGradient id="coinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#FFD700" />
                            <stop offset="100%" stop-color="#FFA500" />
                        </linearGradient>
                    </defs>
                </svg>
            </div>
            
            <h2 class="coin-title">SHIBBEE COINS</h2>
            <p class="coin-subtitle">The next generation of digital currency mining</p>
            
            <div class="countdown-container">
                <div class="countdown-item">
                    <div class="countdown-value" id="days">14</div>
                    <div class="countdown-label">Days</div>
                </div>
                <div class="countdown-item">
                    <div class="countdown-value" id="hours">23</div>
                    <div class="countdown-label">Hours</div>
                </div>
                <div class="countdown-item">
                    <div class="countdown-value" id="minutes">59</div>
                    <div class="countdown-label">Minutes</div>
                </div>
                <div class="countdown-item">
                    <div class="countdown-value" id="seconds">59</div>
                    <div class="countdown-label">Seconds</div>
                </div>
            </div>
            
            <div class="mining-stats">
                <div class="stat-item">
                    <div class="stat-label"><i class="fa fa-tachometer"></i> Mining Rate</div>
                    <div class="stat-value">0.035 SHIB/hour</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label"><i class="fa fa-wallet"></i> Your Balance</div>
                    <div class="stat-value">0.000 SHIB</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label"><i class="fa fa-bolt"></i> Mining Power</div>
                    <div class="stat-value">1.0x</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label"><i class="fa fa-clock"></i> Next Reward</div>
                    <div class="stat-value">--:--:--</div>
                </div>
            </div>
            
            <div class="mining-progress">
                <div class="mining-progress-bar"></div>
            </div>
            
            <button class="mining-button" id="startMiningBtn" disabled>
                <i class="fa fa-play-circle mr-2"></i> Start Mining
            </button>
            
            <button class="mining-button" id="notifyBtn">
                <i class="fa fa-bell mr-2"></i> Notify Me When Live
            </button>
            
            <div class="referral-link">
                <div class="referral-text">https://topupsub.com/shibbee/ref?id=<?php echo isset($profileDetails->sUsername) ? $profileDetails->sUsername : 'user'; ?></div>
                <button class="copy-btn" onclick="copyReferralLink()"><i class="fa fa-copy mr-1"></i> Copy</button>
            </div>
            
            <div class="mining-card-footer">
                <span class="live-indicator"></span> <span id="online-miners">2,547</span> miners waiting for launch
            </div>
        </div>
        
        <div class="mining-info-card">
            <h3 class="mining-info-title">About SHIBBEE COINS</h3>
            <p class="mining-info-text">
                SHIBBEE COINS (SHIB) is our exclusive cryptocurrency designed to revolutionize the way you pay for services on our platform. With its unique dog-inspired design and powerful mining capabilities, SHIBBEE COINS combines fun with functionality, allowing you to earn rewards directly from your mobile device.
            </p>
        </div>
        
        <!-- <div class="mining-info-card">
            <h3 class="mining-info-title">Features</h3>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fa fa-mobile"></i>
                </div>
                <div class="feature-text">
                    <strong>Mobile Mining</strong>
                    Mine SHIBBEE COINS directly from your mobile device without any special hardware. Our energy-efficient algorithm ensures your battery life isn't compromised.
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fa fa-exchange"></i>
                </div>
                <div class="feature-text">
                    <strong>Instant Exchanges</strong>
                    Convert your SHIBBEE COINS to airtime, data, or use for bill payments with zero transaction fees. Enjoy special discounts when paying with SHIB.
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fa fa-users"></i>
                </div>
                <div class="feature-text">
                    <strong>Referral Bonuses</strong>
                    Earn 15% of all SHIBBEE COINS mined by your referrals. Build your mining network and increase your passive income without additional effort.
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fa fa-line-chart"></i>
                </div>
                <div class="feature-text">
                    <strong>Growing Value</strong>
                    SHIBBEE COINS value increases as our user base grows. Early miners will benefit from value appreciation as the ecosystem expands.
                </div>
            </div>
        </div>
        
        <div class="mining-info-card">
            <h3 class="mining-info-title">How It Works</h3>
            <p class="mining-info-text">
                <span class="step-number">1</span> Once launched, tap the "Start Mining" button to begin<br>
                <span class="step-number">2</span> Your device will mine SHIBBEE COINS in the background<br>
                <span class="step-number">3</span> Collect your SHIB rewards every 24 hours<br>
                <span class="step-number">4</span> Use SHIBBEE COINS for discounts on all services<br>
                <span class="step-number">5</span> Refer friends to increase your mining power by up to 5x
            </p>
        </div> -->
    </div>
</div>

<script>
    // Countdown timer
    function updateCountdown() {
        // Set the launch date (15 days from now)
        const launchDate = new Date();
        launchDate.setDate(launchDate.getDate() + 15);
        
        const now = new Date();
        const difference = launchDate - now;
        
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);
        
        document.getElementById('days').innerText = days;
        document.getElementById('hours').innerText = hours.toString().padStart(2, '0');
        document.getElementById('minutes').innerText = minutes.toString().padStart(2, '0');
        document.getElementById('seconds').innerText = seconds.toString().padStart(2, '0');
    }
    
    // Update countdown every second
    setInterval(updateCountdown, 1000);
    updateCountdown();
    
    // Particle animation
    function createParticles() {
        const container = document.getElementById('particles-container');
        const particleCount = 30;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.classList.add('particle');
            
            // Random size between 2-6px
            const size = Math.random() * 4 + 2;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            
            // Random position
            const posX = Math.random() * 100;
            const posY = Math.random() * 100;
            particle.style.left = `${posX}%`;
            particle.style.top = `${posY}%`;
            
            // Random opacity
            particle.style.opacity = Math.random() * 0.5 + 0.1;
            
            // Random color - gold/orange tints
            const hue = 40 + Math.random() * 20; // gold/orange hues
            const saturation = 80 + Math.random() * 20;
            const lightness = 50 + Math.random() * 20;
            particle.style.backgroundColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
            
            // Animation
            const duration = Math.random() * 20 + 10;
            particle.style.animation = `float ${duration}s linear infinite`;
            
            container.appendChild(particle);
            
            // Create floating animation
            const keyframes = `
                @keyframes float {
                    0% {
                        transform: translate(0, 0);
                    }
                    50% {
                        transform: translate(${Math.random() * 30 - 15}px, ${Math.random() * 30 - 15}px);
                    }
                    100% {
                        transform: translate(0, 0);
                    }
                }
            `;
            
            const style = document.createElement('style');
            style.innerHTML = keyframes;
            document.head.appendChild(style);
        }
    }
    
    // Notify button functionality
    document.getElementById('notifyBtn').addEventListener('click', function() {
        swal({
            title: "You're on the list!",
            text: "We'll notify you when SHIBBEE COINS mining goes live!",
            icon: "success",
            button: "Awesome!",
        });
        
        // Change button text after clicking
        this.innerHTML = '<i class="fa fa-check mr-2"></i> Notification Set';
        this.disabled = true;
        
        // Update online miners count (for visual effect)
        const currentMiners = parseInt(document.getElementById('online-miners').textContent);
        document.getElementById('online-miners').textContent = currentMiners + 1;
    });
    
    // Copy referral link
    function copyReferralLink() {
        const referralText = document.querySelector('.referral-text').textContent;
        
        // Create temporary input element
        const tempInput = document.createElement('input');
        tempInput.value = referralText;
        document.body.appendChild(tempInput);
        
        // Select and copy
        tempInput.select();
        document.execCommand('copy');
        
        // Remove temporary element
        document.body.removeChild(tempInput);
        
        // Show success message
        swal({
            title: "Copied!",
            text: "Share your referral link to earn 15% of your friends' mining rewards!",
            icon: "success",
            button: "OK",
            timer: 2000,
        });
    }
    
    // Simulate random online miners count
    function updateOnlineMiners() {
        const currentMiners = parseInt(document.getElementById('online-miners').textContent);
        const change = Math.floor(Math.random() * 5) - 2; // Random change between -2 and +2
        const newCount = Math.max(2500, currentMiners + change);
        document.getElementById('online-miners').textContent = newCount.toLocaleString();
    }
    
    // Update miners count every 5 seconds
    setInterval(updateOnlineMiners, 5000);
    
    // Initialize particles on page load
    window.addEventListener('load', function() {
        createParticles();
        
        // Add some visual flair with random shimmer effects
        setInterval(function() {
            const statValues = document.querySelectorAll('.stat-value');
            const randomStat = statValues[Math.floor(Math.random() * statValues.length)];
            randomStat.style.transition = 'all 0.5s ease';
            randomStat.style.textShadow = '0 0 10px rgba(255, 215, 0, 0.8)';
            
            setTimeout(function() {
                randomStat.style.textShadow = 'none';
            }, 500);
        }, 3000);
    });
</script>
