/* ============================================= 
   Base Styles & Variables
============================================= */
:root {
  /* Color Scheme */
  --primary-color: #4361ee;
  --primary-dark: #3a56d4;
  --secondary-color: #3f37c9;
  --accent-color: #4895ef;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --gray-color: #6c757d;
  --light-gray: #e9ecef;
  --success-color: #4bb543;
  --danger-color: #ff3333;
  --warning-color: #ffc107;
  
  /* Typography */
  --font-main: 'Inter', sans-serif;
  --font-heading: 'Space Grotesk', sans-serif;
  --base-font-size: 1rem;
  --h1-size: 2.5rem;
  --h2-size: 2rem;
  --h3-size: 1.75rem;
  --h4-size: 1.5rem;
  --h5-size: 1.25rem;
  --h6-size: 1rem;
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-xxl: 3rem;
  
  /* Borders */
  --border-radius: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
  --shadow-xl: 0 20px 25px rgba(0,0,0,0.1);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ============================================= 
   Base Styles
============================================= */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-main);
  font-size: var(--base-font-size);
  line-height: 1.6;
  color: var(--dark-color);
  background-color: #fff;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--space-md);
}

h1 { font-size: var(--h1-size); }
h2 { font-size: var(--h2-size); }
h3 { font-size: var(--h3-size); }
h4 { font-size: var(--h4-size); }
h5 { font-size: var(--h5-size); }
h6 { font-size: var(--h6-size); }

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

ul, ol {
  padding-left: var(--space-lg);
}

.container {
  width: 100%;
  padding-right: var(--space-md);
  padding-left: var(--space-md);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

.section {
  padding: var(--space-xxl) 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.section-title {
  color: var(--dark-color);
  margin-bottom: var(--space-sm);
}

.section-subtitle {
  display: inline-block;
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--space-sm);
}

.section-description {
  color: var(--gray-color);
  max-width: 600px;
  margin: 0 auto;
}

/* ============================================= 
   Buttons
============================================= */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.btn i {
  margin-right: 0.5rem;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-primary {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  color: #fff;
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  text-decoration: none;
}

.btn-outline {
  color: var(--primary-color);
  background-color: transparent;
  border-color: var(--primary-color);
}

.btn-outline:hover {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  text-decoration: none;
}

.btn-light {
  color: var(--dark-color);
  background-color: var(--light-color);
  border-color: var(--light-gray);
}

.btn-light:hover {
  color: var(--dark-color);
  background-color: #e2e6ea;
  border-color: #dae0e5;
  text-decoration: none;
}

/* ============================================= 
   Header & Navigation
============================================= */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.header.scrolled {
  box-shadow: var(--shadow-md);
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--dark-color);
}

.logo:hover {
  text-decoration: none;
}

.logo img {
  margin-right: var(--space-sm);
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-sm);
  z-index: 1001;
}

.hamburger {
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--dark-color);
  position: relative;
  transition: all var(--transition-fast);
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 24px;
  height: 2px;
  background-color: var(--dark-color);
  transition: all var(--transition-fast);
}

.hamburger::before {
  top: -8px;
}

.hamburger::after {
  top: 8px;
}

.menu-toggle[aria-expanded="true"] .hamburger {
  background-color: transparent;
}

.menu-toggle[aria-expanded="true"] .hamburger::before {
  transform: rotate(45deg);
  top: 0;
}

.menu-toggle[aria-expanded="true"] .hamburger::after {
  transform: rotate(-45deg);
  top: 0;
}

.navbar-menu {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin-right: var(--space-lg);
}

.nav-link {
  display: block;
  padding: var(--space-sm) var(--space-md);
  font-weight: 500;
  color: var(--dark-color);
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  text-decoration: none;
}

.nav-actions {
  display: flex;
  gap: var(--space-sm);
}

@media (max-width: 991px) {
  .menu-toggle {
    display: block;
  }
  
  .navbar-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    max-width: 320px;
    height: 100vh;
    background-color: #fff;
    box-shadow: var(--shadow-lg);
    flex-direction: column;
    align-items: flex-start;
    padding: var(--space-xxl) var(--space-lg);
    transition: right var(--transition-normal);
    overflow-y: auto;
  }
  
  .navbar-menu.active {
    right: 0;
  }
  
  .nav-list {
    flex-direction: column;
    width: 100%;
    margin-right: 0;
    margin-bottom: var(--space-lg);
  }
  
  .nav-link {
    padding: var(--space-sm) 0;
    border-bottom: 1px solid var(--light-gray);
  }
  
  .nav-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .nav-actions .btn {
    width: 100%;
    margin-bottom: var(--space-sm);
  }
}

/* ============================================= 
   Hero Section
============================================= */
.hero {
  padding-top: 120px;
  padding-bottom: 80px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8ed 100%);
  position: relative;
  overflow: hidden;
}

.hero-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-xl);
  align-items: center;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 2.5rem;
  margin-bottom: var(--space-md);
  color: var(--dark-color);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--gray-color);
  margin-bottom: var(--space-xl);
  max-width: 600px;
}

.hero-cta {
  display: flex;
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.hero-image {
  position: relative;
  z-index: 1;
  text-align: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  margin: 0 auto;
}

.hero-stats {
  display: flex;
  gap: var(--space-xl);
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gray-color);
}

.hero-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  overflow: hidden;
  color: #fff;
}

.hero-wave svg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@media (min-width: 992px) {
  .hero {
    padding-top: 150px;
    padding-bottom: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
  }
  
  .hero-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .hero-title {
    font-size: 3.5rem;
  }
}

/* ============================================= 
   Features & Value Cards
============================================= */
.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.value-card {
  background-color: #fff;
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  text-align: center;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.card-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto var(--space-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  font-size: 1.5rem;
}

.value-card h3 {
  margin-bottom: var(--space-sm);
}

/* ============================================= 
   Stats Section
============================================= */
.stats {
  background-color: var(--primary-color);
  color: #fff;
  padding: var(--space-xl) 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: var(--space-xs);
}

.stat-symbol {
  font-size: 1.5rem;
  margin-left: 0.25rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
}

/* ============================================= 
   Features Section
============================================= */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-xl);
  align-items: center;
}

.features-image {
  text-align: center;
}

.features-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-lg);
}

.feature-item {
  display: flex;
  gap: var(--space-md);
}

.feature-icon {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--primary-color);
  border-radius: var(--border-radius);
  font-size: 1.25rem;
}

.feature-content h3 {
  margin-bottom: var(--space-xs);
}

@media (min-width: 992px) {
  .features-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* ============================================= 
   Pricing Section
============================================= */
.pricing-tabs {
  margin-bottom: var(--space-xl);
}

.tab-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-lg);
  border-bottom: 1px solid var(--light-gray);
}

.tab-button {
  padding: var(--space-sm) var(--space-lg);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-weight: 600;
  color: var(--gray-color);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.tab-button:hover {
  color: var(--primary-color);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
}

.pricing-card {
  background-color: #fff;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--space-lg);
  color: #fff;
  text-align: center;
}

.card-header i {
  font-size: 2rem;
  margin-bottom: var(--space-sm);
}

.card-header h3 {
  color: inherit;
  margin-bottom: 0;
}

.card-body {
  padding: var(--space-lg);
}

.price {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  margin-bottom: var(--space-lg);
}

.amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
}

.duration {
  font-size: 1rem;
  color: var(--gray-color);
  align-self: flex-end;
  margin-left: 0.25rem;
}

.features-list {
  list-style: none;
  padding-left: 0;
  margin-bottom: var(--space-lg);
}

.features-list li {
  margin-bottom: var(--space-sm);
  display: flex;
  align-items: center;
}

.features-list i {
  color: var(--success-color);
  margin-right: var(--space-sm);
}

.card-footer {
  padding: 0 var(--space-lg) var(--space-lg);
}

.pricing-cta {
  text-align: center;
  margin-top: var(--space-xl);
}

/* ============================================= 
   FAQ Section
============================================= */
.faq-grid {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  margin-bottom: var(--space-sm);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.faq-question {
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  background-color: #fff;
  border: none;
  text-align: left;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.faq-question:hover {
  background-color: var(--light-color);
}

.faq-question[aria-expanded="true"] {
  background-color: var(--light-color);
}

.faq-question[aria-expanded="true"] i {
  transform: rotate(180deg);
}

.faq-question i {
  transition: transform var(--transition-fast);
}

.faq-answer {
  padding: 0 var(--space-lg);
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal), padding var(--transition-normal);
}

.faq-answer p {
  padding-bottom: var(--space-md);
}

.faq-question[aria-expanded="true"] + .faq-answer {
  max-height: 300px;
  padding-top: 0;
  padding-bottom: var(--space-md);
}

.faq-cta {
  text-align: center;
  margin-top: var(--space-xl);
}

/* ============================================= 
   CTA Section
============================================= */
.cta {
  background-color: var(--primary-color);
  color: #fff;
}

.cta-card {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  text-align: center;
}

.cta-card h2 {
  color: #fff;
  margin-bottom: var(--space-md);
}

.cta-card p {
  margin-bottom: var(--space-xl);
  opacity: 0.9;
}

@media (min-width: 768px) {
  .cta-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: left;
    padding: var(--space-xxl);
  }
  
  .cta-content {
    flex: 1;
    margin-right: var(--space-xl);
  }
  
  .cta-action {
    flex-shrink: 0;
  }
}

/* ============================================= 
   Footer
============================================= */
.footer {
  background-color: var(--dark-color);
  color: #fff;
  padding-top: var(--space-xxl);
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-xxl);
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-md);
  color: #fff;
}

.footer-logo:hover {
  text-decoration: none;
}

.footer-logo img {
  margin-right: var(--space-sm);
}

.footer-about-text {
  margin-bottom: var(--space-md);
  opacity: 0.8;
}

.footer-social {
  display: flex;
  gap: var(--space-sm);
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-radius: 50%;
  transition: background-color var(--transition-fast);
}

.footer-social a:hover {
  background-color: var(--primary-color);
  text-decoration: none;
}

.footer-title {
  font-size: 1.25rem;
  margin-bottom: var(--space-lg);
  color: #fff;
}

.footer-links ul {
  list-style: none;
  padding-left: 0;
}

.footer-links li {
  margin-bottom: var(--space-sm);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: #fff;
  text-decoration: none;
}

.footer-contact li {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-md);
}

.footer-contact i {
  margin-right: var(--space-sm);
  color: var(--primary-color);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--space-lg) 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.footer-copyright {
  margin-bottom: var(--space-md);
  opacity: 0.7;
  font-size: 0.875rem;
}

.footer-legal {
  display: flex;
  gap: var(--space-lg);
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  transition: color var(--transition-fast);
}

.footer-legal a:hover {
  color: #fff;
  text-decoration: none;
}

@media (min-width: 768px) {
  .footer-bottom {
    flex-direction: row;
  }
  
  .footer-copyright {
    margin-bottom: 0;
  }
}

/* ============================================= 
   Utility Classes
============================================= */
.skip-link {
  position: absolute;
  top: -100px;
  left: 0;
  background-color: var(--primary-color);
  color: #fff;
  padding: var(--space-sm) var(--space-md);
  z-index: 9999;
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 0;
}

.back-to-top {
  position: fixed;
  bottom: var(--space-lg);
  right: var(--space-lg);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 50%;
  box-shadow: var(--shadow-md);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  color: #fff;
  background-color: var(--primary-dark);
  text-decoration: none;
}

.noscript-warning {
  padding: var(--space-sm);
  background-color: var(--warning-color);
  color: var(--dark-color);
  text-align: center;
  font-weight: 500;
}

/* ============================================= 
   Animations
============================================= */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* ============================================= 
   Dark Mode
============================================= */
@media (prefers-color-scheme: dark) {
  body {
    color: #f8f9fa;
    background-color: #121212;
  }
  
  .header {
    background-color: rgba(18, 18, 18, 0.95);
  }
  
  .logo, .nav-link {
    color: #f8f9fa;
  }
  
  .hamburger,
  .hamburger::before,
  .hamburger::after {
    background-color: #f8f9fa;
  }
  
  .section-title,
  .hero-title,
  .value-card h3,
  .feature-content h3,
  .pricing-card h3,
  .amount {
    color: #f8f9fa;
  }
  
  .section-description,
  .hero-subtitle,
  .stat-label,
  .duration {
    color: rgba(248, 249, 250, 0.7);
  }
  
  .value-card,
  .pricing-card,
  .card-body,
  .faq-question {
    background-color: #1e1e1e;
  }
  
  .hero {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }
  
  .faq-item {
    border-color: #333;
  }
  
  .footer {
    background-color: #1a1a1a;
  }
  
  .footer-links a,
  .footer-about-text,
  .footer-copyright,
  .footer-legal a {
    color: rgba(255, 255, 255, 0.7);
  }
  
  .footer-links a:hover,
  .footer-legal a:hover {
    color: #fff;
  }
}