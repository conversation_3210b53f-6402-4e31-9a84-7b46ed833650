<?php

if (isset($_POST['check'])) {
	session_start();
	$prft = intval($_SESSION['config']['cable_profit']);
	$id = $_POST['cablename'];

	if ($id == 1) {
		die(json_encode($gotv));
	}elseif ($id == 2) {
		die(json_encode($dstv));
	}elseif ($id == 3) {
		die(json_encode($startime));
	}
}

if (isset($_POST['checkCable'])) {
	session_start();
	$iuc = $_POST['iuc'];
	$cable = $_POST['cable'];
	$sCable = '';
	if ($cable == 1) {
		$sCable = 'GOTV';
	}elseif ($cable == 2) {
		$sCable = 'DSTV';
	}elseif ($cable == 3) {
		$sCable = 'STARTIMES';
	}

	


	$curl = curl_init();

	curl_setopt_array($curl, array(
		CURLOPT_URL => "https://$_SERVER[HTTP_HOST]/api_admin/api/cableVerify",
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => array('decoder_name' => $sCable,'card_number' => $iuc),
		CURLOPT_HTTPHEADER => array(
			'Authorization: Token e7209548ec58fcd8ee3d80731cf32837'
		),
	));

	$response = curl_exec($curl);

	curl_close($curl);




	$response = curl_exec($curl);

	curl_close($curl);
	$res = json_decode($response);
	if ($res->error) {
		die(json_encode([false, $res->desc]));
	}elseif($res->error == false){
		die(json_encode([true, $res->name]));
	}else{
		die(json_encode([false, 'Something Went Wrong']));
	}
}


if (isset($_POST['getcable'])) {
	session_start();
// card_number
// service_id
// decoder_name
	$prft = intval($_SESSION['config']['cable_profit']);
	
	$amt;
	include "../../conn.php";
	$email = $_SESSION["data"]["email"];
	$balDet = mysqli_fetch_assoc(mysqli_query($con, "SELECT bal FROM users WHERE email = '$email'"));
	$iuc = $_POST['iuc'];
	$cable = $_POST['cable'];
	$plan = $_POST['plan'];
	$name = $_POST['name'];
	$sCable;
	if ($cable == '1') {
		$sCable = 'GOTV';
	}elseif ($cable == '2') {
		$sCable = 'DSTV';
	}elseif ($cable == '3') {
		$sCable = 'STARTIMES';
	}
	$cbq = mysqli_query($con, "SELECT * FROM cables WHERE id = '$plan'");
	$cb = mysqli_fetch_assoc($cbq);
	if (mysqli_num_rows($cbq) != 1) {
		die('455');
	}
	$amt = $cb['amount'];
	$pl = $cb['plan_id'];
	$pl_n = $cb['name'];
	if (empty($amt)) {
		die('455');
	}
	if (intval($balDet['bal']) < intval($amt)) {
		die('223');
	}

	// $curl = curl_init();

	// curl_setopt_array($curl, array(
	// 	CURLOPT_URL => 'http://localhost/app.vtudata/api_admin/api/cableBuy',
	// 	CURLOPT_RETURNTRANSFER => true,
	// 	CURLOPT_ENCODING => '',
	// 	CURLOPT_MAXREDIRS => 10,
	// 	CURLOPT_TIMEOUT => 0,
	// 	CURLOPT_FOLLOWLOCATION => true,
	// 	CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	// 	CURLOPT_CUSTOMREQUEST => 'POST',
	// 	CURLOPT_POSTFIELDS => array('card_number' => $iuc,'service_id' => $pl,'decoder_name' => $sCable),
	// 	CURLOPT_HTTPHEADER => array(
	// 		'Authorization: Token '.$_SESSION["config"]["api_token"]
	// 	),
	// ));

	// $response = curl_exec($curl);

	// curl_close($curl);

	$curl = curl_init();

	curl_setopt_array($curl, array(
		CURLOPT_URL => "https://$_SERVER[HTTP_HOST]/api_admin/api/cableBuy",
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => array('card_number' => $iuc,'service_id' => $pl,'decoder_name' => $sCable),
		CURLOPT_HTTPHEADER => array(
			'Authorization: Token e7209548ec58fcd8ee3d80731cf32837'
		),
	));

	$response = curl_exec($curl);

	curl_close($curl);

	// $response = '{"error": false, "desc": "SSSS"}';

$myfile = fopen("RESOURCE.txt", "a") or die("Unable to open file!");
    fwrite($myfile, '
        '.$response.'
        ');
    fclose($myfile);
	$res = json_decode($response);
	$t = time();
	$ref = md5($t);
	$username = $username = $_SESSION["data"]["username"];
	if (isset($res->error) && $res->error || !isset($res->error)) {
		die(isset($res->error) ? $res->desc : '5557678');
	}elseif(isset($res->error) && $res->error == false){
		$amt = strval(intval($amt));
		$userT = mysqli_fetch_assoc(mysqli_query($con, "SELECT type FROM users WHERE email = '$email'"));
		$cable_c = mysqli_fetch_array(mysqli_query($con, "SELECT * FROM cable_config"));
		$usertype = $userT['type'];
		$r = $cable_c[$usertype];
		$new_amt = 0;
		if ($cable_c['method'] == 'percentage') {
			$r = intval($r);
			$new_amt = ($r/100) * intval($amt);
		}elseif($cable_c['method'] == 'discount'){
			$r = intval($r);
			$new_amt = 0 - $r;
		}
		
		// $profit = intval($prft);
		// $oldPro = mysqli_fetch_assoc(mysqli_query($con, "SELECT profit FROM config"));
		// $newPro = strval(intval($oldPro['profit']) + $profit);
		$oldBal = strval($balDet['bal']);
		$newBal = strval(intval($oldBal) -(intval($amt) - $new_amt));
		$amt_paid = strval(intval($amt) - $new_amt);
		$cable_det = json_encode(array('cable' => $sCable, 'card' => $iuc, 'plan' => $pl_n, 'amount' => $amt, 'name' => $name, 'date' => $current_date, 'status' => 'success'));
		mysqli_query($con, "UPDATE users SET bal = '$newBal' WHERE email = '$email'");
		mysqli_query($con, "INSERT INTO transactions (type, t_desc, ref, status, amount, oldBal, newBal, email, username, date_time, amount_paid) VALUES ('Cable Payment',  '$cable_det', '$ref', 'success', '$amt', '$oldBal', '$newBal', '$email', '$username', '$current_date', '$amt_paid')");
		// mysqli_query($con, "UPDATE config SET profit = '$newPro'");
		die('200');
	}
	
}

































?>

<?php 
// Get primary WhatsApp number for widget
$whatsapp_numbers = [];
if (isset($config['whatsapp_num']) && !empty($config['whatsapp_num'])) {
    // Try decoding as JSON array first
    $decoded_whatsapp = json_decode($config['whatsapp_num'], true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_whatsapp)) {
        $whatsapp_numbers = $decoded_whatsapp;
    } elseif (is_string($config['whatsapp_num'])) {
         // Fallback: treat as a single number if not valid JSON
        $whatsapp_numbers = [$config['whatsapp_num']];
    }
}

// Get primary WhatsApp number for widget
$primary_whatsapp = !empty($whatsapp_numbers) ? $whatsapp_numbers[0] : '';
// Clean the number (remove non-digits)
$primary_whatsapp_clean = preg_replace('/[^0-9]/', '', $primary_whatsapp);
// Ensure it starts with country code, assuming 234 if it starts with 0 and is local length
if (substr($primary_whatsapp_clean, 0, 1) === '0' && (strlen($primary_whatsapp_clean) == 11 || strlen($primary_whatsapp_clean) == 10)) {
    $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 1);
} elseif (substr($primary_whatsapp_clean, 0, 3) === '234' && strlen($primary_whatsapp_clean) > 13) {
    // Handle cases like +2340... by removing the 0
    $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 3);
} elseif (substr($primary_whatsapp_clean, 0, 1) === '+') {
    $primary_whatsapp_clean = substr($primary_whatsapp_clean, 1); // Remove leading + if present
}
?>

<?php if (!empty($primary_whatsapp_clean)): ?>
<div class="whatsapp-widget">
    <!-- Floating chat button -->
    <button class="whatsapp-button" id="openWhatsappChat">
        <i class="bi bi-whatsapp"></i>
    </button>

    <!-- Chat popup -->
    <div class="whatsapp-popup" id="whatsappChatPopup">
        <div class="popup-header">
            <div class="popup-header-content">
                <img src="../../sharesublogo.png" alt="<?= htmlspecialchars($config['site_name']) ?>" width="40">
                <div>
                    <h6 class="mb-0"><?= htmlspecialchars($config['site_name']) ?> Support</h6>
                    <small class="text-muted">Usually replies within an hour</small>
                </div>
            </div>
            <button class="close-btn" id="closeWhatsappChat">×</button>
        </div>
        <div class="popup-body">
            <div class="message-container">
                <div class="received-message">
                    <p>Hello! How can we help you today?</p>
                    <small class="message-time">Now</small>
                </div>
            </div>
            <div class="message-input">
                <form id="whatsappMessageForm">
                    <input type="text" id="whatsappMessage" placeholder="Type a message..." required>
                    <button type="submit">
                        <i class="bi bi-send-fill"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- WhatsApp Widget Styles -->
<style>
    .whatsapp-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }

    .whatsapp-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #25D366;
        color: white;
        border: none;
        font-size: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        transition: all 0.3s;
    }

    .whatsapp-button:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 14px rgba(0, 0, 0, 0.2);
    }

    .whatsapp-popup {
        position: absolute;
        bottom: 80px;
        right: 0;
        width: 320px;
        background-color: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
        display: none;
    }

    .popup-header {
        background-color: #25D366;
        color: white;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .popup-header-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .popup-header img {
        border-radius: 50%;
        background-color: white;
        padding: 5px;
    }

    .close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
    }

    .popup-body {
        display: flex;
        flex-direction: column;
        height: 300px;
    }

    .message-container {
        flex-grow: 1;
        padding: 15px;
        overflow-y: auto;
    }

    .received-message,
    .sent-message {
        max-width: 80%;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 10px;
    }

    .received-message {
        background-color: #f0f0f0;
        align-self: flex-start;
    }

    .sent-message {
        background-color: #dcf8c6;
        align-self: flex-end;
        margin-left: auto;
    }

    .message-time {
        display: block;
        font-size: 10px;
        margin-top: 5px;
        opacity: 0.6;
    }

    .message-input {
        border-top: 1px solid #e0e0e0;
        padding: 10px;
    }

    .message-input form {
        display: flex;
    }

    .message-input input {
        flex-grow: 1;
        border: none;
        padding: 10px;
        outline: none;
    }

    .message-input button {
        background-color: transparent;
        border: none;
        color: #25D366;
        cursor: pointer;
        font-size: 20px;
    }
</style>

<!-- WhatsApp Widget Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const openBtn = document.getElementById('openWhatsappChat');
        const closeBtn = document.getElementById('closeWhatsappChat');
        const popup = document.getElementById('whatsappChatPopup');
        const messageForm = document.getElementById('whatsappMessageForm');
        const messageInput = document.getElementById('whatsappMessage');
        const messageContainer = document.querySelector('.message-container');
        
        // WhatsApp number from database
        const whatsappNumber = '<?= $primary_whatsapp_clean ?>';
        
        // Toggle chat popup
        openBtn.addEventListener('click', function() {
            popup.style.display = 'block';
        });
        
        closeBtn.addEventListener('click', function() {
            popup.style.display = 'none';
        });
        
        // Handle message sending
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (message) {
                // Add sent message to chat
                const sentMsg = document.createElement('div');
                sentMsg.className = 'sent-message';
                sentMsg.innerHTML = `
                    <p>${message}</p>
                    <small class="message-time">Just now</small>
                `;
                messageContainer.appendChild(sentMsg);
                
                // Clear input
                messageInput.value = '';
                
                // Scroll to bottom
                messageContainer.scrollTop = messageContainer.scrollHeight;
                
                // Open WhatsApp in new tab after a brief delay
                setTimeout(() => {
                    const encodedMessage = encodeURIComponent(message);
                    window.open(`https://wa.me/${whatsappNumber}?text=${encodedMessage}`, '_blank');
                    
                    // Hide popup after sending
                    popup.style.display = 'none';
                }, 500);
            }
        });
    });
</script>
<?php endif; ?>