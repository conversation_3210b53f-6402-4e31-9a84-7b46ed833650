<?php
$actual_link = $_SERVER['REMOTE_ADDR'];
//if($actual_link != '198.187.31.83') die('Invalid REQUEST');

//include "../../conn.php";

if ($_SERVER["REQUEST_METHOD"] == "GET") {
	die(json_encode(array('error' => true, 'desc' => 'Unsupported Method')));
}
$headers = apache_request_headers();
if (!isset($headers['Authorization']) || empty($headers['Authorization'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
}

if (!isset($_POST['phone_number']) || empty($_POST['phone_number'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid phone_number')));
}

if (!isset($_POST['meter_number']) || empty($_POST['meter_number'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid meter_number')));
}

if (!isset($_POST['type']) || empty($_POST['type'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid type')));
}

if (!isset($_POST['service_id']) || empty($_POST['service_id'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid service_id')));
}

if (!isset($_POST['amount']) || empty($_POST['amount'])) {
	die(json_encode(array('error' => true, 'desc' => 'Invalid amount')));
}
$phone_number = mysqli_real_escape_string($con, $_POST['phone_number']);
$meter_number = mysqli_real_escape_string($con, $_POST['meter_number']);
$service_id = mysqli_real_escape_string($con, $_POST['service_id']);
$amount = mysqli_real_escape_string($con, $_POST['amount']);
$mtype = mysqli_real_escape_string($con, $_POST['type']);

// $req_token = mysqli_real_escape_string($con, $headers['Authorization']);

// if (!str_contains($req_token, 'Token')) {
// 	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
// }

// if (!isset(explode(' ', $req_token)[1]) || empty(explode(' ', $req_token)[1])) {
// 	die(json_encode(array('error' => true, 'desc' => 'Invalid Authorization Token')));
// }

// $token = explode(' ', $req_token)[1];

if (strtolower($mtype) != 'prepaid' && strtolower($mtype) != 'postpaid') {
	die(json_encode(array('error' => true, 'desc' => 'Invalid type')));
}

$bill_conf = mysqli_fetch_array(mysqli_query($con, "SELECT * FROM bill_config"));
$usertype = $user['type'];

$type = 'ELECTRICITY PURCHASE';
$t_desc = "ELECTRICITY OF";
$status = 'pending';
$t_date = $current_date;
$ref = md5($t_date);
$channel = strtoupper($service_id);

$netsToken = mysqli_query($con, "SELECT * FROM apikey WHERE platform='vtpass' AND types='airtime' ");
$tokenKey = mysqli_fetch_assoc($netsToken);
if (!$tokenKey) {
	http_response_code(400);
	die(json_encode(array('error' => true, 'desc' => 'Invalid apikey')));
}
$apiLink = $tokenKey['apiLink'];
$apiKey = $tokenKey['apiKey'];
$secretkey = $tokenKey['secretkey'];


if ($bill_conf['source'] == 'vtpass') {
	$responseD = fromVtpass($ref, $service_id, $amount, $phone_number, $mtype, $meter_number, $apiLink, $apiKey, $secretkey);
	$resp = json_decode($responseD);
	$platform = 'VTPASS';
	if ($resp->code == '000') {

		$status = 'success';
		mysqli_query($con, "INSERT INTO api_transactions (type, t_desc, status, t_date, ref, channel, platform, detail) VALUES ('$type', '$t_desc', '$status', '$t_date', '$ref', '$channel', '$platform', '$responseD')");

		die(json_encode(array('error' => false, 'status' => 202, 'desc' => $t_desc . ' Placed Successfully', 'token' => $resp->purchased_code)));
	} else {
		$status = 'failed';
		mysqli_query($con, "INSERT INTO api_transactions (type, t_desc, status, t_date, ref, channel, platform, detail) VALUES ('$type', '$t_desc', '$status', '$t_date', '$ref', '$channel', '$platform', '$responseD')");
		die(json_encode(array('error' => true, 'status' => 400, 'desc' => $resp->response_description)));
	}
}

function fromVtpass($ref, $service_id, $amount, $phone, $type, $meter_number, $apiLink, $apiKey, $secretkey)
{
	$curl = curl_init();
	$payload = ['api-key: ' . $apiKey, 'secret-key: ' . $secretkey];
	curl_setopt_array(
		$curl,
		array(
			CURLOPT_URL => $apiLink,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => array('request_id' => $ref, 'serviceID' => $service_id, 'amount' => $amount, 'phone' => $phone, 'variation_code' => $type, 'billersCode' => $meter_number),
			CURLOPT_HTTPHEADER => $payload
		),
	);

file_put_contents('aaa.txt' ,"$ref, $service_id, $amount, $phone, $type, $meter_number, $apiLink, $apiKey, $secretkey" );


	$response = curl_exec($curl);

	curl_close($curl);
	file_put_contents('aaa2.txt', $response);
	return $response;
}
