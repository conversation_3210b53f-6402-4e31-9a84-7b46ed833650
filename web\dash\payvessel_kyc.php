<?php
include 'header.php';

// Check if user already has KYC completed
$kyc_done = !empty($data['BVN']) || !empty($data['NIN']);
?>

<div class="container-fluid mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Payvessel KYC Verification</h3>
          <p class="card-description">Complete your KYC to generate virtual account numbers</p>
        </div>
        <div class="card-body">
          
          <?php if ($kyc_done): ?>
            <div class="alert alert-success mb-4">
              <i class="fa fa-check-circle"></i>
              <strong>KYC Verification Completed!</strong><br>
              Your KYC verification is already complete. You can update your information below or generate virtual account numbers.
              <br><br>
              <a href="payvessel_virtual.php" class="btn btn-success">
                <i class="fa fa-university"></i> Generate Virtual Accounts
              </a>
            </div>
          <?php endif; ?>
            
            <div class="row">
              <div class="col-md-6">
                <div class="card bg-light">
                  <div class="card-body">
                    <h5 class="card-title">Why KYC Verification?</h5>
                    <ul class="list-unstyled">
                      <li><i class="fa fa-check text-success"></i> Required for virtual account generation</li>
                      <li><i class="fa fa-check text-success"></i> Ensures account security</li>
                      <li><i class="fa fa-check text-success"></i> Complies with banking regulations</li>
                      <li><i class="fa fa-check text-success"></i> Enables instant wallet funding</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div class="col-md-6">
                <form id="kycForm">
                  <div class="form-group">
                    <label for="bvnNumber">Bank Verification Number (BVN)</label>
                    <input type="text" class="form-control" id="bvnNumber" name="bvnNumber" 
                           placeholder="Enter your 11-digit BVN" maxlength="11" 
                           value="<?= htmlspecialchars($data['BVN'] ?? '') ?>">
                    <small class="form-text text-muted">
                      Enter your 11-digit Bank Verification Number
                    </small>
                  </div>
                  
                  <div class="form-group">
                    <label for="ninNumber">National Identity Number (NIN)</label>
                    <input type="text" class="form-control" id="ninNumber" name="ninNumber" 
                           placeholder="Enter your 11-digit NIN" maxlength="11"
                           value="<?= htmlspecialchars($data['NIN'] ?? '') ?>">
                    <small class="form-text text-muted">
                      Enter your 11-digit National Identity Number
                    </small>
                  </div>
                  
                  <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fa fa-user-check"></i> <?= $kyc_done ? 'Update KYC Information' : 'Submit KYC Verification' ?>
                  </button>
                </form>
              </div>
            </div>
          
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.getElementById('kycForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const bvnNumber = document.getElementById('bvnNumber').value;
  const ninNumber = document.getElementById('ninNumber').value;
  
  // Check if at least one field is filled
  if (!bvnNumber && !ninNumber) {
    Swal.fire('Error', 'Please enter either BVN or NIN (or both)', 'error');
    return;
  }
  
  // Validate BVN if provided
  if (bvnNumber && bvnNumber.length !== 11) {
    Swal.fire('Error', 'BVN must be 11 digits', 'error');
    return;
  }
  
  // Validate NIN if provided
  if (ninNumber && ninNumber.length !== 11) {
    Swal.fire('Error', 'NIN must be 11 digits', 'error');
    return;
  }
  
  // Show loading
  Swal.fire({
    title: 'Submitting KYC',
    text: 'Please wait while we verify your information...',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });
  
  // Submit KYC data
  $.ajax({
    url: 'payvessel_kyc_verify.php',
    method: 'POST',
    data: {
      bvn: bvnNumber,
      nin: ninNumber
    },
    success: function(response) {
      try {
        const result = JSON.parse(response);
        if (result.success) {
          Swal.fire({
            title: 'KYC Verification Successful!',
            text: 'Your KYC verification has been completed successfully.',
            icon: 'success'
          }).then(() => {
            window.location.href = 'payvessel_virtual.php';
          });
        } else {
          Swal.fire('Error', result.message || 'KYC verification failed', 'error');
        }
      } catch (e) {
        Swal.fire('Error', 'Invalid response from server', 'error');
      }
    },
    error: function() {
      Swal.fire('Error', 'Failed to connect to server', 'error');
    }
  });
});

// Auto-format BVN input
document.getElementById('bvnNumber').addEventListener('input', function(e) {
  let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
  if (value.length > 11) {
    value = value.substring(0, 11);
  }
  e.target.value = value;
});

// Auto-format NIN input
document.getElementById('ninNumber').addEventListener('input', function(e) {
  let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
  if (value.length > 11) {
    value = value.substring(0, 11);
  }
  e.target.value = value;
});
</script> 