<?php
// Webhook Diagnostic Script
header('Content-Type: text/html');

echo "<h2>Payvessel Webhook Diagnostic</h2>";

// Test 1: Basic PHP functionality
echo "<h3>1. Basic PHP Test</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current Time: " . date('Y-m-d H:i:s') . "<br>";
echo "Server: " . $_SERVER['SERVER_NAME'] . "<br>";
echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "<br>";

// Test 2: File system
echo "<h3>2. File System Test</h3>";
$webhookFile = 'payvesselWebHook.php';
echo "Webhook file exists: " . (file_exists($webhookFile) ? '✅ Yes' : '❌ No') . "<br>";
echo "Webhook file readable: " . (is_readable($webhookFile) ? '✅ Yes' : '❌ No') . "<br>";
echo "Webhook file size: " . (file_exists($webhookFile) ? filesize($webhookFile) . ' bytes' : 'N/A') . "<br>";

// Test 3: Database connection
echo "<h3>3. Database Connection Test</h3>";
try {
    include 'conn.php';
    echo "Database connection: ✅ Success<br>";
    
    // Test config table
    $config = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM config"));
    if ($config) {
        echo "Config table: ✅ Success<br>";
        echo "Payvessel config exists: " . (isset($config['payvessel']) ? '✅ Yes' : '❌ No') . "<br>";
    } else {
        echo "Config table: ❌ Failed<br>";
    }
    
    // Test users table
    $users = mysqli_query($con, "SELECT COUNT(*) as count FROM users");
    if ($users) {
        $userCount = mysqli_fetch_assoc($users)['count'];
        echo "Users table: ✅ Success (Count: $userCount)<br>";
    } else {
        echo "Users table: ❌ Failed<br>";
    }
    
    // Test transactions table
    $transactions = mysqli_query($con, "SELECT COUNT(*) as count FROM transactions");
    if ($transactions) {
        $transCount = mysqli_fetch_assoc($transactions)['count'];
        echo "Transactions table: ✅ Success (Count: $transCount)<br>";
    } else {
        echo "Transactions table: ❌ Failed<br>";
    }
    
} catch (Exception $e) {
    echo "Database connection: ❌ Failed - " . $e->getMessage() . "<br>";
}

// Test 4: PHP Extensions
echo "<h3>4. PHP Extensions Test</h3>";
echo "cURL extension: " . (extension_loaded('curl') ? '✅ Loaded' : '❌ Not loaded') . "<br>";
echo "JSON extension: " . (extension_loaded('json') ? '✅ Loaded' : '❌ Not loaded') . "<br>";
echo "MySQLi extension: " . (extension_loaded('mysqli') ? '✅ Loaded' : '❌ Not loaded') . "<br>";

// Test 5: Error reporting
echo "<h3>5. Error Reporting Test</h3>";
echo "Error reporting level: " . error_reporting() . "<br>";
echo "Display errors: " . ini_get('display_errors') . "<br>";
echo "Log errors: " . ini_get('log_errors') . "<br>";
echo "Error log file: " . ini_get('error_log') . "<br>";

// Test 6: Test webhook functionality
echo "<h3>6. Webhook Functionality Test</h3>";
echo "<form method='POST' action='payvesselWebHook_simple.php'>";
echo "<input type='hidden' name='test' value='1'>";
echo "<button type='submit'>Test Simple Webhook</button>";
echo "</form>";

// Test 7: Simulate webhook payload
echo "<h3>7. Simulate Webhook Payload</h3>";
$testPayload = [
    "transaction" => [
        "date" => "2025-08-04T00:00:00",
        "reference" => "TEST_" . time(),
        "sessionid" => "TEST_" . time()
    ],
    "order" => [
        "currency" => "NGN",
        "amount" => "100",
        "fee" => "1.00",
        "description" => "Test transfer",
        "settlement_amount" => "99.00"
    ],
    "customer" => [
        "email" => "<EMAIL>",
        "phone" => "***********"
    ],
    "virtualAccount" => [
        "virtualAccountNumber" => "**********",
        "virtualBank" => "999991"
    ],
    "sender" => [
        "senderAccountNumber" => "**********",
        "senderBankName" => "MONIEPOINT",
        "senderName" => "HASSAN ABUBAKAR",
        "SenderBankCode" => null
    ],
    "message" => "Success",
    "code" => "00"
];

echo "<form method='POST' action='payvesselWebHook_simple.php'>";
echo "<input type='hidden' name='payload' value='" . htmlspecialchars(json_encode($testPayload)) . "'>";
echo "<button type='submit'>Test with Sample Payload</button>";
echo "</form>";

echo "<br><br><strong>Diagnostic completed!</strong>";
?> 