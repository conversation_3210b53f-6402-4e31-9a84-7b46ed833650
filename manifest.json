{"name": "Share Sub", "short_name": "SHARE SUB", "start_url": "https://sharesubdata.com.ng/web/app-/login", "display": "standalone", "description": "sharesubdata.com.ng is a one stop solution for you airtime , data, and elelctricty bill payments", "lang": "en", "dir": "auto", "theme_color": "#FFFFFF", "background_color": "#FFFFFF", "orientation": "any", "icons": [{"src": "https://www.pwabuilder.com/assets/icons/icon_512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}, {"src": "https://www.pwabuilder.com/assets/icons/icon_192.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}], "screenshots": [{"src": "https://www.pwabuilder.com/assets/screenshots/screen1.png", "sizes": "2880x1800", "type": "image/png", "description": "A screenshot of the home page"}], "related_applications": [{"platform": "windows", "url": " The URL to your app in that app store"}], "prefer_related_applications": false, "shortcuts": [{"name": "The name you would like to be displayed for your shortcut", "url": "The url you would like to open when the user chooses this shortcut. This must be a URL local to your PWA. For example: If my start_url is /, this URL must be something like /shortcut", "description": "A description of the functionality of this shortcut"}]}