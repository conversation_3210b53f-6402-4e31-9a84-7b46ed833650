{"version": 3, "file": "workbox-routing.dev.js", "sources": ["../_version.js", "../utils/constants.js", "../utils/normalizeHandler.js", "../Route.js", "../NavigationRoute.js", "../RegExpRoute.js", "../Router.js", "../utils/getOrCreateDefaultRouter.js", "../registerRoute.js", "../setCatchHandler.js", "../setDefaultHandler.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:routing:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The default HTTP method, 'GET', used when there's no specific method\n * configured for a route.\n *\n * @type {string}\n *\n * @private\n */\nexport const defaultMethod = 'GET';\n/**\n * The list of valid HTTP methods associated with requests that could be routed.\n *\n * @type {Array<string>}\n *\n * @private\n */\nexport const validMethods = [\n    'DELETE',\n    'GET',\n    'HEAD',\n    'PATCH',\n    'POST',\n    'PUT',\n];\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {function()|Object} handler Either a function, or an object with a\n * 'handle' method.\n * @return {Object} An object with a handle method.\n *\n * @private\n */\nexport const normalizeHandler = (handler) => {\n    if (handler && typeof handler === 'object') {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.hasMethod(handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return handler;\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(handler, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return { handle: handler };\n    }\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { defaultMethod, validMethods } from './utils/constants.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport './_version.js';\n/**\n * A `Route` consists of a pair of callback functions, \"match\" and \"handler\".\n * The \"match\" callback determine if a route should be used to \"handle\" a\n * request by returning a non-falsy value if it can. The \"handler\" callback\n * is called when there is a match and should return a Promise that resolves\n * to a `Response`.\n *\n * @memberof workbox-routing\n */\nclass Route {\n    /**\n     * Constructor for Route class.\n     *\n     * @param {workbox-routing~matchCallback} match\n     * A callback function that determines whether the route matches a given\n     * `fetch` event by returning a non-falsy value.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(match, handler, method = defaultMethod) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(match, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'match',\n            });\n            if (method) {\n                assert.isOneOf(method, validMethods, { paramName: 'method' });\n            }\n        }\n        // These values are referenced directly by Router so cannot be\n        // altered by minificaton.\n        this.handler = normalizeHandler(handler);\n        this.match = match;\n        this.method = method;\n    }\n    /**\n     *\n     * @param {workbox-routing-handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response\n     */\n    setCatchHandler(handler) {\n        this.catchHandler = normalizeHandler(handler);\n    }\n}\nexport { Route };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * NavigationRoute makes it easy to create a\n * {@link workbox-routing.Route} that matches for browser\n * [navigation requests]{@link https://developers.google.com/web/fundamentals/primers/service-workers/high-performance-loading#first_what_are_navigation_requests}.\n *\n * It will only match incoming Requests whose\n * {@link https://fetch.spec.whatwg.org/#concept-request-mode|mode}\n * is set to `navigate`.\n *\n * You can optionally only apply this route to a subset of navigation requests\n * by using one or both of the `denylist` and `allowlist` parameters.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass NavigationRoute extends Route {\n    /**\n     * If both `denylist` and `allowlist` are provided, the `denylist` will\n     * take precedence and the request will not match this route.\n     *\n     * The regular expressions in `allowlist` and `denylist`\n     * are matched against the concatenated\n     * [`pathname`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/pathname}\n     * and [`search`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/search}\n     * portions of the requested URL.\n     *\n     * *Note*: These RegExps may be evaluated against every destination URL during\n     * a navigation. Avoid using\n     * [complex RegExps](https://github.com/GoogleChrome/workbox/issues/3077),\n     * or else your users may see delays when navigating your site.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {Object} options\n     * @param {Array<RegExp>} [options.denylist] If any of these patterns match,\n     * the route will not handle the request (even if a allowlist RegExp matches).\n     * @param {Array<RegExp>} [options.allowlist=[/./]] If any of these patterns\n     * match the URL's pathname and search parameter, the route will handle the\n     * request (assuming the denylist doesn't match).\n     */\n    constructor(handler, { allowlist = [/./], denylist = [] } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArrayOfClass(allowlist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.allowlist',\n            });\n            assert.isArrayOfClass(denylist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.denylist',\n            });\n        }\n        super((options) => this._match(options), handler);\n        this._allowlist = allowlist;\n        this._denylist = denylist;\n    }\n    /**\n     * Routes match handler.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {Request} options.request\n     * @return {boolean}\n     *\n     * @private\n     */\n    _match({ url, request }) {\n        if (request && request.mode !== 'navigate') {\n            return false;\n        }\n        const pathnameAndSearch = url.pathname + url.search;\n        for (const regExp of this._denylist) {\n            if (regExp.test(pathnameAndSearch)) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                        `being used, since the URL matches this denylist pattern: ` +\n                        `${regExp.toString()}`);\n                }\n                return false;\n            }\n        }\n        if (this._allowlist.some((regExp) => regExp.test(pathnameAndSearch))) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`The navigation route ${pathnameAndSearch} ` + `is being used.`);\n            }\n            return true;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                `being used, since the URL being navigated to doesn't ` +\n                `match the allowlist.`);\n        }\n        return false;\n    }\n}\nexport { NavigationRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * RegExpRoute makes it easy to create a regular expression based\n * {@link workbox-routing.Route}.\n *\n * For same-origin requests the RegExp only needs to match part of the URL. For\n * requests against third-party servers, you must define a RegExp that matches\n * the start of the URL.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass RegExpRoute extends Route {\n    /**\n     * If the regular expression contains\n     * [capture groups]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#grouping-back-references},\n     * the captured values will be passed to the\n     * {@link workbox-routing~handlerCallback} `params`\n     * argument.\n     *\n     * @param {RegExp} regExp The regular expression to match against URLs.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(regExp, handler, method) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(regExp, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'RegExpRoute',\n                funcName: 'constructor',\n                paramName: 'pattern',\n            });\n        }\n        const match = ({ url }) => {\n            const result = regExp.exec(url.href);\n            // Return immediately if there's no match.\n            if (!result) {\n                return;\n            }\n            // Require that the match start at the first character in the URL string\n            // if it's a cross-origin request.\n            // See https://github.com/GoogleChrome/workbox/issues/281 for the context\n            // behind this behavior.\n            if (url.origin !== location.origin && result.index !== 0) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`The regular expression '${regExp.toString()}' only partially matched ` +\n                        `against the cross-origin URL '${url.toString()}'. RegExpRoute's will only ` +\n                        `handle cross-origin requests if they match the entire URL.`);\n                }\n                return;\n            }\n            // If the route matches, but there aren't any capture groups defined, then\n            // this will return [], which is truthy and therefore sufficient to\n            // indicate a match.\n            // If there are capture groups, then it will return their values.\n            return result.slice(1);\n        };\n        super(match, handler, method);\n    }\n}\nexport { RegExpRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { defaultMethod } from './utils/constants.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * The Router can be used to process a `FetchEvent` using one or more\n * {@link workbox-routing.Route}, responding with a `Response` if\n * a matching route exists.\n *\n * If no route matches a given a request, the Router will use a \"default\"\n * handler if one is defined.\n *\n * Should the matching Route throw an error, the Router will use a \"catch\"\n * handler if one is defined to gracefully deal with issues and respond with a\n * Request.\n *\n * If a request matches multiple routes, the **earliest** registered route will\n * be used to respond to the request.\n *\n * @memberof workbox-routing\n */\nclass Router {\n    /**\n     * Initializes a new Router.\n     */\n    constructor() {\n        this._routes = new Map();\n        this._defaultHandlerMap = new Map();\n    }\n    /**\n     * @return {Map<string, Array<workbox-routing.Route>>} routes A `Map` of HTTP\n     * method name ('GET', etc.) to an array of all the corresponding `Route`\n     * instances that are registered.\n     */\n    get routes() {\n        return this._routes;\n    }\n    /**\n     * Adds a fetch event listener to respond to events when a route matches\n     * the event's request.\n     */\n    addFetchListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('fetch', ((event) => {\n            const { request } = event;\n            const responsePromise = this.handleRequest({ request, event });\n            if (responsePromise) {\n                event.respondWith(responsePromise);\n            }\n        }));\n    }\n    /**\n     * Adds a message event listener for URLs to cache from the window.\n     * This is useful to cache resources loaded on the page prior to when the\n     * service worker started controlling it.\n     *\n     * The format of the message data sent from the window should be as follows.\n     * Where the `urlsToCache` array may consist of URL strings or an array of\n     * URL string + `requestInit` object (the same as you'd pass to `fetch()`).\n     *\n     * ```\n     * {\n     *   type: 'CACHE_URLS',\n     *   payload: {\n     *     urlsToCache: [\n     *       './script1.js',\n     *       './script2.js',\n     *       ['./script3.js', {mode: 'no-cors'}],\n     *     ],\n     *   },\n     * }\n     * ```\n     */\n    addCacheListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('message', ((event) => {\n            // event.data is type 'any'\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            if (event.data && event.data.type === 'CACHE_URLS') {\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                const { payload } = event.data;\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`Caching URLs from the window`, payload.urlsToCache);\n                }\n                const requestPromises = Promise.all(payload.urlsToCache.map((entry) => {\n                    if (typeof entry === 'string') {\n                        entry = [entry];\n                    }\n                    const request = new Request(...entry);\n                    return this.handleRequest({ request, event });\n                    // TODO(philipwalton): TypeScript errors without this typecast for\n                    // some reason (probably a bug). The real type here should work but\n                    // doesn't: `Array<Promise<Response> | undefined>`.\n                })); // TypeScript\n                event.waitUntil(requestPromises);\n                // If a MessageChannel was used, reply to the message on success.\n                if (event.ports && event.ports[0]) {\n                    void requestPromises.then(() => event.ports[0].postMessage(true));\n                }\n            }\n        }));\n    }\n    /**\n     * Apply the routing rules to a FetchEvent object to get a Response from an\n     * appropriate Route's handler.\n     *\n     * @param {Object} options\n     * @param {Request} options.request The request to handle.\n     * @param {ExtendableEvent} options.event The event that triggered the\n     *     request.\n     * @return {Promise<Response>|undefined} A promise is returned if a\n     *     registered route can handle the request. If there is no matching\n     *     route and there's no `defaultHandler`, `undefined` is returned.\n     */\n    handleRequest({ request, event, }) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'handleRequest',\n                paramName: 'options.request',\n            });\n        }\n        const url = new URL(request.url, location.href);\n        if (!url.protocol.startsWith('http')) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Workbox Router only supports URLs that start with 'http'.`);\n            }\n            return;\n        }\n        const sameOrigin = url.origin === location.origin;\n        const { params, route } = this.findMatchingRoute({\n            event,\n            request,\n            sameOrigin,\n            url,\n        });\n        let handler = route && route.handler;\n        const debugMessages = [];\n        if (process.env.NODE_ENV !== 'production') {\n            if (handler) {\n                debugMessages.push([`Found a route to handle this request:`, route]);\n                if (params) {\n                    debugMessages.push([\n                        `Passing the following params to the route's handler:`,\n                        params,\n                    ]);\n                }\n            }\n        }\n        // If we don't have a handler because there was no matching route, then\n        // fall back to defaultHandler if that's defined.\n        const method = request.method;\n        if (!handler && this._defaultHandlerMap.has(method)) {\n            if (process.env.NODE_ENV !== 'production') {\n                debugMessages.push(`Failed to find a matching route. Falling ` +\n                    `back to the default handler for ${method}.`);\n            }\n            handler = this._defaultHandlerMap.get(method);\n        }\n        if (!handler) {\n            if (process.env.NODE_ENV !== 'production') {\n                // No handler so Workbox will do nothing. If logs is set of debug\n                // i.e. verbose, we should print out this information.\n                logger.debug(`No route found for: ${getFriendlyURL(url)}`);\n            }\n            return;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            // We have a handler, meaning Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Router is responding to: ${getFriendlyURL(url)}`);\n            debugMessages.forEach((msg) => {\n                if (Array.isArray(msg)) {\n                    logger.log(...msg);\n                }\n                else {\n                    logger.log(msg);\n                }\n            });\n            logger.groupEnd();\n        }\n        // Wrap in try and catch in case the handle method throws a synchronous\n        // error. It should still callback to the catch handler.\n        let responsePromise;\n        try {\n            responsePromise = handler.handle({ url, request, event, params });\n        }\n        catch (err) {\n            responsePromise = Promise.reject(err);\n        }\n        // Get route's catch handler, if it exists\n        const catchHandler = route && route.catchHandler;\n        if (responsePromise instanceof Promise &&\n            (this._catchHandler || catchHandler)) {\n            responsePromise = responsePromise.catch(async (err) => {\n                // If there's a route catch handler, process that first\n                if (catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to route's Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    try {\n                        return await catchHandler.handle({ url, request, event, params });\n                    }\n                    catch (catchErr) {\n                        if (catchErr instanceof Error) {\n                            err = catchErr;\n                        }\n                    }\n                }\n                if (this._catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to global Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    return this._catchHandler.handle({ url, request, event });\n                }\n                throw err;\n            });\n        }\n        return responsePromise;\n    }\n    /**\n     * Checks a request and URL (and optionally an event) against the list of\n     * registered routes, and if there's a match, returns the corresponding\n     * route along with any params generated by the match.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {boolean} options.sameOrigin The result of comparing `url.origin`\n     *     against the current origin.\n     * @param {Request} options.request The request to match.\n     * @param {Event} options.event The corresponding event.\n     * @return {Object} An object with `route` and `params` properties.\n     *     They are populated if a matching route was found or `undefined`\n     *     otherwise.\n     */\n    findMatchingRoute({ url, sameOrigin, request, event, }) {\n        const routes = this._routes.get(request.method) || [];\n        for (const route of routes) {\n            let params;\n            // route.match returns type any, not possible to change right now.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const matchResult = route.match({ url, sameOrigin, request, event });\n            if (matchResult) {\n                if (process.env.NODE_ENV !== 'production') {\n                    // Warn developers that using an async matchCallback is almost always\n                    // not the right thing to do.\n                    if (matchResult instanceof Promise) {\n                        logger.warn(`While routing ${getFriendlyURL(url)}, an async ` +\n                            `matchCallback function was used. Please convert the ` +\n                            `following route to use a synchronous matchCallback function:`, route);\n                    }\n                }\n                // See https://github.com/GoogleChrome/workbox/issues/2079\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                params = matchResult;\n                if (Array.isArray(params) && params.length === 0) {\n                    // Instead of passing an empty array in as params, use undefined.\n                    params = undefined;\n                }\n                else if (matchResult.constructor === Object && // eslint-disable-line\n                    Object.keys(matchResult).length === 0) {\n                    // Instead of passing an empty object in as params, use undefined.\n                    params = undefined;\n                }\n                else if (typeof matchResult === 'boolean') {\n                    // For the boolean value true (rather than just something truth-y),\n                    // don't set params.\n                    // See https://github.com/GoogleChrome/workbox/pull/2134#issuecomment-513924353\n                    params = undefined;\n                }\n                // Return early if have a match.\n                return { route, params };\n            }\n        }\n        // If no match was found above, return and empty object.\n        return {};\n    }\n    /**\n     * Define a default `handler` that's called when no routes explicitly\n     * match the incoming request.\n     *\n     * Each HTTP method ('GET', 'POST', etc.) gets its own default handler.\n     *\n     * Without a default handler, unmatched requests will go against the\n     * network as if there were no service worker present.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to associate with this\n     * default handler. Each method has its own default.\n     */\n    setDefaultHandler(handler, method = defaultMethod) {\n        this._defaultHandlerMap.set(method, normalizeHandler(handler));\n    }\n    /**\n     * If a Route throws an error while handling a request, this `handler`\n     * will be called and given a chance to provide a response.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     */\n    setCatchHandler(handler) {\n        this._catchHandler = normalizeHandler(handler);\n    }\n    /**\n     * Registers a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to register.\n     */\n    registerRoute(route) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(route, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route, 'match', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.isType(route.handler, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route.handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.handler',\n            });\n            assert.isType(route.method, 'string', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.method',\n            });\n        }\n        if (!this._routes.has(route.method)) {\n            this._routes.set(route.method, []);\n        }\n        // Give precedence to all of the earlier routes by adding this additional\n        // route to the end of the array.\n        this._routes.get(route.method).push(route);\n    }\n    /**\n     * Unregisters a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to unregister.\n     */\n    unregisterRoute(route) {\n        if (!this._routes.has(route.method)) {\n            throw new WorkboxError('unregister-route-but-not-found-with-method', {\n                method: route.method,\n            });\n        }\n        const routeIndex = this._routes.get(route.method).indexOf(route);\n        if (routeIndex > -1) {\n            this._routes.get(route.method).splice(routeIndex, 1);\n        }\n        else {\n            throw new WorkboxError('unregister-route-route-not-registered');\n        }\n    }\n}\nexport { Router };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Router } from '../Router.js';\nimport '../_version.js';\nlet defaultRouter;\n/**\n * Creates a new, singleton Router instance if one does not exist. If one\n * does already exist, that instance is returned.\n *\n * @private\n * @return {Router}\n */\nexport const getOrCreateDefaultRouter = () => {\n    if (!defaultRouter) {\n        defaultRouter = new Router();\n        // The helpers that use the default Router assume these listeners exist.\n        defaultRouter.addFetchListener();\n        defaultRouter.addCacheListener();\n    }\n    return defaultRouter;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Route } from './Route.js';\nimport { RegExpRoute } from './RegExpRoute.js';\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Easily register a RegExp, string, or function with a caching\n * strategy to a singleton Router instance.\n *\n * This method will generate a Route for you if needed and\n * call {@link workbox-routing.Router#registerRoute}.\n *\n * @param {RegExp|string|workbox-routing.Route~matchCallback|workbox-routing.Route} capture\n * If the capture param is a `Route`, all other arguments will be ignored.\n * @param {workbox-routing~handlerCallback} [handler] A callback\n * function that returns a Promise resulting in a Response. This parameter\n * is required if `capture` is not a `Route` object.\n * @param {string} [method='GET'] The HTTP method to match the Route\n * against.\n * @return {workbox-routing.Route} The generated `Route`.\n *\n * @memberof workbox-routing\n */\nfunction registerRoute(capture, handler, method) {\n    let route;\n    if (typeof capture === 'string') {\n        const captureUrl = new URL(capture, location.href);\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(capture.startsWith('/') || capture.startsWith('http'))) {\n                throw new WorkboxError('invalid-string', {\n                    moduleName: 'workbox-routing',\n                    funcName: 'registerRoute',\n                    paramName: 'capture',\n                });\n            }\n            // We want to check if Express-style wildcards are in the pathname only.\n            // TODO: Remove this log message in v4.\n            const valueToCheck = capture.startsWith('http')\n                ? captureUrl.pathname\n                : capture;\n            // See https://github.com/pillarjs/path-to-regexp#parameters\n            const wildcards = '[*:?+]';\n            if (new RegExp(`${wildcards}`).exec(valueToCheck)) {\n                logger.debug(`The '$capture' parameter contains an Express-style wildcard ` +\n                    `character (${wildcards}). Strings are now always interpreted as ` +\n                    `exact matches; use a RegExp for partial or wildcard matches.`);\n            }\n        }\n        const matchCallback = ({ url }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (url.pathname === captureUrl.pathname &&\n                    url.origin !== captureUrl.origin) {\n                    logger.debug(`${capture} only partially matches the cross-origin URL ` +\n                        `${url.toString()}. This route will only handle cross-origin requests ` +\n                        `if they match the entire URL.`);\n                }\n            }\n            return url.href === captureUrl.href;\n        };\n        // If `capture` is a string then `handler` and `method` must be present.\n        route = new Route(matchCallback, handler, method);\n    }\n    else if (capture instanceof RegExp) {\n        // If `capture` is a `RegExp` then `handler` and `method` must be present.\n        route = new RegExpRoute(capture, handler, method);\n    }\n    else if (typeof capture === 'function') {\n        // If `capture` is a function then `handler` and `method` must be present.\n        route = new Route(capture, handler, method);\n    }\n    else if (capture instanceof Route) {\n        route = capture;\n    }\n    else {\n        throw new WorkboxError('unsupported-route-type', {\n            moduleName: 'workbox-routing',\n            funcName: 'registerRoute',\n            paramName: 'capture',\n        });\n    }\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.registerRoute(route);\n    return route;\n}\nexport { registerRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * If a Route throws an error while handling a request, this `handler`\n * will be called and given a chance to provide a response.\n *\n * @param {workbox-routing~handlerCallback} handler A callback\n * function that returns a Promise resulting in a Response.\n *\n * @memberof workbox-routing\n */\nfunction setCatchHandler(handler) {\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.setCatchHandler(handler);\n}\nexport { setCatchHandler };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Define a default `handler` that's called when no routes explicitly\n * match the incoming request.\n *\n * Without a default handler, unmatched requests will go against the\n * network as if there were no service worker present.\n *\n * @param {workbox-routing~handlerCallback} handler A callback\n * function that returns a Promise resulting in a Response.\n *\n * @memberof workbox-routing\n */\nfunction setDefaultHandler(handler) {\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.setDefaultHandler(handler);\n}\nexport { setDefaultHandler };\n"], "names": ["self", "_", "e", "defaultMethod", "validMethods", "normalize<PERSON><PERSON><PERSON>", "handler", "assert", "has<PERSON><PERSON><PERSON>", "moduleName", "className", "funcName", "paramName", "isType", "handle", "Route", "constructor", "match", "method", "isOneOf", "setCatchHandler", "<PERSON><PERSON><PERSON><PERSON>", "NavigationRoute", "allowlist", "denylist", "isArrayOfClass", "RegExp", "options", "_match", "_allowlist", "_denylist", "url", "request", "mode", "pathnameAndSearch", "pathname", "search", "regExp", "test", "logger", "log", "toString", "some", "debug", "RegExpRoute", "isInstance", "result", "exec", "href", "origin", "location", "index", "slice", "Router", "_routes", "Map", "_defaultHandlerMap", "routes", "addFetchListener", "addEventListener", "event", "responsePromise", "handleRequest", "respondWith", "addCacheListener", "data", "type", "payload", "urlsToCache", "requestPromises", "Promise", "all", "map", "entry", "Request", "waitUntil", "ports", "then", "postMessage", "URL", "protocol", "startsWith", "<PERSON><PERSON><PERSON><PERSON>", "params", "route", "findMatchingRoute", "debugMessages", "push", "has", "get", "getFriendlyURL", "groupCollapsed", "for<PERSON>ach", "msg", "Array", "isArray", "groupEnd", "err", "reject", "_catch<PERSON><PERSON>ler", "catch", "error", "catchErr", "Error", "matchResult", "warn", "length", "undefined", "Object", "keys", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "registerRoute", "unregisterRoute", "WorkboxError", "routeIndex", "indexOf", "splice", "defaultRouter", "getOrCreateDefaultRouter", "capture", "captureUrl", "valueToCheck", "wildcards", "matchCallback"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,uBAAuB,CAAC,IAAIC,CAAC,EAAE,CAAA;IACxC,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,MAAMC,aAAa,GAAG,KAAK,CAAA;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACO,MAAMC,YAAY,GAAG,CACxB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,CACR;;IC/BD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACzC,EAAA,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IACxC,IAA2C;IACvCC,MAAAA,gBAAM,CAACC,SAAS,CAACF,OAAO,EAAE,QAAQ,EAAE;IAChCG,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,OAAO;IAClBC,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,OAAON,OAAO,CAAA;IAClB,GAAC,MACI;IACD,IAA2C;IACvCC,MAAAA,gBAAM,CAACM,MAAM,CAACP,OAAO,EAAE,UAAU,EAAE;IAC/BG,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,OAAO;IAClBC,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,OAAO;IAAEE,MAAAA,MAAM,EAAER,OAAAA;SAAS,CAAA;IAC9B,GAAA;IACJ,CAAC;;ICvCD;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMS,KAAK,CAAC;IACR;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIC,WAAWA,CAACC,KAAK,EAAEX,OAAO,EAAEY,MAAM,GAAGf,aAAa,EAAE;IAChD,IAA2C;IACvCI,MAAAA,gBAAM,CAACM,MAAM,CAACI,KAAK,EAAE,UAAU,EAAE;IAC7BR,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,OAAO;IAClBC,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,OAAA;IACf,OAAC,CAAC,CAAA;IACF,MAAA,IAAIM,MAAM,EAAE;IACRX,QAAAA,gBAAM,CAACY,OAAO,CAACD,MAAM,EAAEd,YAAY,EAAE;IAAEQ,UAAAA,SAAS,EAAE,QAAA;IAAS,SAAC,CAAC,CAAA;IACjE,OAAA;IACJ,KAAA;IACA;IACA;IACA,IAAA,IAAI,CAACN,OAAO,GAAGD,gBAAgB,CAACC,OAAO,CAAC,CAAA;QACxC,IAAI,CAACW,KAAK,GAAGA,KAAK,CAAA;QAClB,IAAI,CAACC,MAAM,GAAGA,MAAM,CAAA;IACxB,GAAA;IACA;IACJ;IACA;IACA;IACA;MACIE,eAAeA,CAACd,OAAO,EAAE;IACrB,IAAA,IAAI,CAACe,YAAY,GAAGhB,gBAAgB,CAACC,OAAO,CAAC,CAAA;IACjD,GAAA;IACJ;;IC1DA;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMgB,eAAe,SAASP,KAAK,CAAC;IAChC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIC,WAAWA,CAACV,OAAO,EAAE;QAAEiB,SAAS,GAAG,CAAC,GAAG,CAAC;IAAEC,IAAAA,QAAQ,GAAG,EAAA;OAAI,GAAG,EAAE,EAAE;IAC5D,IAA2C;IACvCjB,MAAAA,gBAAM,CAACkB,cAAc,CAACF,SAAS,EAAEG,MAAM,EAAE;IACrCjB,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,iBAAiB;IAC5BC,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,mBAAA;IACf,OAAC,CAAC,CAAA;IACFL,MAAAA,gBAAM,CAACkB,cAAc,CAACD,QAAQ,EAAEE,MAAM,EAAE;IACpCjB,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,iBAAiB;IAC5BC,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,kBAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,KAAK,CAAEe,OAAO,IAAK,IAAI,CAACC,MAAM,CAACD,OAAO,CAAC,EAAErB,OAAO,CAAC,CAAA;QACjD,IAAI,CAACuB,UAAU,GAAGN,SAAS,CAAA;QAC3B,IAAI,CAACO,SAAS,GAAGN,QAAQ,CAAA;IAC7B,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACII,EAAAA,MAAMA,CAAC;QAAEG,GAAG;IAAEC,IAAAA,OAAAA;IAAQ,GAAC,EAAE;IACrB,IAAA,IAAIA,OAAO,IAAIA,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;IACxC,MAAA,OAAO,KAAK,CAAA;IAChB,KAAA;QACA,MAAMC,iBAAiB,GAAGH,GAAG,CAACI,QAAQ,GAAGJ,GAAG,CAACK,MAAM,CAAA;IACnD,IAAA,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACP,SAAS,EAAE;IACjC,MAAA,IAAIO,MAAM,CAACC,IAAI,CAACJ,iBAAiB,CAAC,EAAE;IAChC,QAA2C;IACvCK,UAAAA,gBAAM,CAACC,GAAG,CAAE,CAAuBN,qBAAAA,EAAAA,iBAAkB,UAAS,GACzD,CAAA,yDAAA,CAA0D,GAC1D,CAAA,EAAEG,MAAM,CAACI,QAAQ,EAAG,EAAC,CAAC,CAAA;IAC/B,SAAA;IACA,QAAA,OAAO,KAAK,CAAA;IAChB,OAAA;IACJ,KAAA;IACA,IAAA,IAAI,IAAI,CAACZ,UAAU,CAACa,IAAI,CAAEL,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACJ,iBAAiB,CAAC,CAAC,EAAE;IAClE,MAA2C;YACvCK,gBAAM,CAACI,KAAK,CAAE,CAAA,qBAAA,EAAuBT,iBAAkB,CAAE,CAAA,CAAA,GAAI,gBAAe,CAAC,CAAA;IACjF,OAAA;IACA,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;IACA,IAA2C;UACvCK,gBAAM,CAACC,GAAG,CAAE,CAAuBN,qBAAAA,EAAAA,iBAAkB,UAAS,GACzD,CAAA,qDAAA,CAAsD,GACtD,CAAA,oBAAA,CAAqB,CAAC,CAAA;IAC/B,KAAA;IACA,IAAA,OAAO,KAAK,CAAA;IAChB,GAAA;IACJ;;IC5GA;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMU,WAAW,SAAS7B,KAAK,CAAC;IAC5B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAWA,CAACqB,MAAM,EAAE/B,OAAO,EAAEY,MAAM,EAAE;IACjC,IAA2C;IACvCX,MAAAA,gBAAM,CAACsC,UAAU,CAACR,MAAM,EAAEX,MAAM,EAAE;IAC9BjB,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,aAAa;IACxBC,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAMK,KAAK,GAAGA,CAAC;IAAEc,MAAAA,GAAAA;IAAI,KAAC,KAAK;UACvB,MAAMe,MAAM,GAAGT,MAAM,CAACU,IAAI,CAAChB,GAAG,CAACiB,IAAI,CAAC,CAAA;IACpC;UACA,IAAI,CAACF,MAAM,EAAE;IACT,QAAA,OAAA;IACJ,OAAA;IACA;IACA;IACA;IACA;IACA,MAAA,IAAIf,GAAG,CAACkB,MAAM,KAAKC,QAAQ,CAACD,MAAM,IAAIH,MAAM,CAACK,KAAK,KAAK,CAAC,EAAE;IACtD,QAA2C;cACvCZ,gBAAM,CAACI,KAAK,CAAE,CAAA,wBAAA,EAA0BN,MAAM,CAACI,QAAQ,EAAG,CAAA,yBAAA,CAA0B,GAC/E,CAAgCV,8BAAAA,EAAAA,GAAG,CAACU,QAAQ,EAAG,CAA4B,2BAAA,CAAA,GAC3E,4DAA2D,CAAC,CAAA;IACrE,SAAA;IACA,QAAA,OAAA;IACJ,OAAA;IACA;IACA;IACA;IACA;IACA,MAAA,OAAOK,MAAM,CAACM,KAAK,CAAC,CAAC,CAAC,CAAA;SACzB,CAAA;IACD,IAAA,KAAK,CAACnC,KAAK,EAAEX,OAAO,EAAEY,MAAM,CAAC,CAAA;IACjC,GAAA;IACJ;;ICvEA;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMmC,MAAM,CAAC;IACT;IACJ;IACA;IACIrC,EAAAA,WAAWA,GAAG;IACV,IAAA,IAAI,CAACsC,OAAO,GAAG,IAAIC,GAAG,EAAE,CAAA;IACxB,IAAA,IAAI,CAACC,kBAAkB,GAAG,IAAID,GAAG,EAAE,CAAA;IACvC,GAAA;IACA;IACJ;IACA;IACA;IACA;MACI,IAAIE,MAAMA,GAAG;QACT,OAAO,IAAI,CAACH,OAAO,CAAA;IACvB,GAAA;IACA;IACJ;IACA;IACA;IACII,EAAAA,gBAAgBA,GAAG;IACf;IACA1D,IAAAA,IAAI,CAAC2D,gBAAgB,CAAC,OAAO,EAAIC,KAAK,IAAK;UACvC,MAAM;IAAE5B,QAAAA,OAAAA;IAAQ,OAAC,GAAG4B,KAAK,CAAA;IACzB,MAAA,MAAMC,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC;YAAE9B,OAAO;IAAE4B,QAAAA,KAAAA;IAAM,OAAC,CAAC,CAAA;IAC9D,MAAA,IAAIC,eAAe,EAAE;IACjBD,QAAAA,KAAK,CAACG,WAAW,CAACF,eAAe,CAAC,CAAA;IACtC,OAAA;IACJ,KAAE,CAAC,CAAA;IACP,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIG,EAAAA,gBAAgBA,GAAG;IACf;IACAhE,IAAAA,IAAI,CAAC2D,gBAAgB,CAAC,SAAS,EAAIC,KAAK,IAAK;IACzC;IACA;UACA,IAAIA,KAAK,CAACK,IAAI,IAAIL,KAAK,CAACK,IAAI,CAACC,IAAI,KAAK,YAAY,EAAE;IAChD;YACA,MAAM;IAAEC,UAAAA,OAAAA;aAAS,GAAGP,KAAK,CAACK,IAAI,CAAA;IAC9B,QAA2C;cACvC1B,gBAAM,CAACI,KAAK,CAAE,CAAA,4BAAA,CAA6B,EAAEwB,OAAO,CAACC,WAAW,CAAC,CAAA;IACrE,SAAA;IACA,QAAA,MAAMC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACJ,OAAO,CAACC,WAAW,CAACI,GAAG,CAAEC,KAAK,IAAK;IACnE,UAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;gBAC3BA,KAAK,GAAG,CAACA,KAAK,CAAC,CAAA;IACnB,WAAA;IACA,UAAA,MAAMzC,OAAO,GAAG,IAAI0C,OAAO,CAAC,GAAGD,KAAK,CAAC,CAAA;cACrC,OAAO,IAAI,CAACX,aAAa,CAAC;gBAAE9B,OAAO;IAAE4B,YAAAA,KAAAA;IAAM,WAAC,CAAC,CAAA;IAC7C;IACA;IACA;aACH,CAAC,CAAC,CAAC;IACJA,QAAAA,KAAK,CAACe,SAAS,CAACN,eAAe,CAAC,CAAA;IAChC;YACA,IAAIT,KAAK,CAACgB,KAAK,IAAIhB,KAAK,CAACgB,KAAK,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAA,KAAKP,eAAe,CAACQ,IAAI,CAAC,MAAMjB,KAAK,CAACgB,KAAK,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;IACrE,SAAA;IACJ,OAAA;IACJ,KAAE,CAAC,CAAA;IACP,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIhB,EAAAA,aAAaA,CAAC;QAAE9B,OAAO;IAAE4B,IAAAA,KAAAA;IAAO,GAAC,EAAE;IAC/B,IAA2C;IACvCrD,MAAAA,gBAAM,CAACsC,UAAU,CAACb,OAAO,EAAE0C,OAAO,EAAE;IAChCjE,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBC,QAAAA,SAAS,EAAE,iBAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,MAAMmB,GAAG,GAAG,IAAIgD,GAAG,CAAC/C,OAAO,CAACD,GAAG,EAAEmB,QAAQ,CAACF,IAAI,CAAC,CAAA;QAC/C,IAAI,CAACjB,GAAG,CAACiD,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;IAClC,MAA2C;IACvC1C,QAAAA,gBAAM,CAACI,KAAK,CAAE,CAAA,yDAAA,CAA0D,CAAC,CAAA;IAC7E,OAAA;IACA,MAAA,OAAA;IACJ,KAAA;QACA,MAAMuC,UAAU,GAAGnD,GAAG,CAACkB,MAAM,KAAKC,QAAQ,CAACD,MAAM,CAAA;QACjD,MAAM;UAAEkC,MAAM;IAAEC,MAAAA,KAAAA;IAAM,KAAC,GAAG,IAAI,CAACC,iBAAiB,CAAC;UAC7CzB,KAAK;UACL5B,OAAO;UACPkD,UAAU;IACVnD,MAAAA,GAAAA;IACJ,KAAC,CAAC,CAAA;IACF,IAAA,IAAIzB,OAAO,GAAG8E,KAAK,IAAIA,KAAK,CAAC9E,OAAO,CAAA;QACpC,MAAMgF,aAAa,GAAG,EAAE,CAAA;IACxB,IAA2C;IACvC,MAAA,IAAIhF,OAAO,EAAE;YACTgF,aAAa,CAACC,IAAI,CAAC,CAAE,uCAAsC,EAAEH,KAAK,CAAC,CAAC,CAAA;IACpE,QAAA,IAAID,MAAM,EAAE;cACRG,aAAa,CAACC,IAAI,CAAC,CACd,sDAAqD,EACtDJ,MAAM,CACT,CAAC,CAAA;IACN,SAAA;IACJ,OAAA;IACJ,KAAA;IACA;IACA;IACA,IAAA,MAAMjE,MAAM,GAAGc,OAAO,CAACd,MAAM,CAAA;QAC7B,IAAI,CAACZ,OAAO,IAAI,IAAI,CAACkD,kBAAkB,CAACgC,GAAG,CAACtE,MAAM,CAAC,EAAE;IACjD,MAA2C;YACvCoE,aAAa,CAACC,IAAI,CAAE,CAAA,yCAAA,CAA0C,GACzD,CAAkCrE,gCAAAA,EAAAA,MAAO,GAAE,CAAC,CAAA;IACrD,OAAA;UACAZ,OAAO,GAAG,IAAI,CAACkD,kBAAkB,CAACiC,GAAG,CAACvE,MAAM,CAAC,CAAA;IACjD,KAAA;QACA,IAAI,CAACZ,OAAO,EAAE;IACV,MAA2C;IACvC;IACA;YACAiC,gBAAM,CAACI,KAAK,CAAE,CAAA,oBAAA,EAAsB+C,gCAAc,CAAC3D,GAAG,CAAE,CAAA,CAAC,CAAC,CAAA;IAC9D,OAAA;IACA,MAAA,OAAA;IACJ,KAAA;IACA,IAA2C;IACvC;IACA;UACAQ,gBAAM,CAACoD,cAAc,CAAE,CAAA,yBAAA,EAA2BD,gCAAc,CAAC3D,GAAG,CAAE,CAAA,CAAC,CAAC,CAAA;IACxEuD,MAAAA,aAAa,CAACM,OAAO,CAAEC,GAAG,IAAK;IAC3B,QAAA,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IACpBtD,UAAAA,gBAAM,CAACC,GAAG,CAAC,GAAGqD,GAAG,CAAC,CAAA;IACtB,SAAC,MACI;IACDtD,UAAAA,gBAAM,CAACC,GAAG,CAACqD,GAAG,CAAC,CAAA;IACnB,SAAA;IACJ,OAAC,CAAC,CAAA;UACFtD,gBAAM,CAACyD,QAAQ,EAAE,CAAA;IACrB,KAAA;IACA;IACA;IACA,IAAA,IAAInC,eAAe,CAAA;QACnB,IAAI;IACAA,MAAAA,eAAe,GAAGvD,OAAO,CAACQ,MAAM,CAAC;YAAEiB,GAAG;YAAEC,OAAO;YAAE4B,KAAK;IAAEuB,QAAAA,MAAAA;IAAO,OAAC,CAAC,CAAA;SACpE,CACD,OAAOc,GAAG,EAAE;IACRpC,MAAAA,eAAe,GAAGS,OAAO,CAAC4B,MAAM,CAACD,GAAG,CAAC,CAAA;IACzC,KAAA;IACA;IACA,IAAA,MAAM5E,YAAY,GAAG+D,KAAK,IAAIA,KAAK,CAAC/D,YAAY,CAAA;QAChD,IAAIwC,eAAe,YAAYS,OAAO,KACjC,IAAI,CAAC6B,aAAa,IAAI9E,YAAY,CAAC,EAAE;IACtCwC,MAAAA,eAAe,GAAGA,eAAe,CAACuC,KAAK,CAAC,MAAOH,GAAG,IAAK;IACnD;IACA,QAAA,IAAI5E,YAAY,EAAE;IACd,UAA2C;IACvC;IACA;gBACAkB,gBAAM,CAACoD,cAAc,CAAE,CAAkC,iCAAA,CAAA,GACpD,CAAGD,CAAAA,EAAAA,gCAAc,CAAC3D,GAAG,CAAE,CAAA,wCAAA,CAAyC,CAAC,CAAA;IACtEQ,YAAAA,gBAAM,CAAC8D,KAAK,CAAE,CAAiB,gBAAA,CAAA,EAAEjB,KAAK,CAAC,CAAA;IACvC7C,YAAAA,gBAAM,CAAC8D,KAAK,CAACJ,GAAG,CAAC,CAAA;gBACjB1D,gBAAM,CAACyD,QAAQ,EAAE,CAAA;IACrB,WAAA;cACA,IAAI;IACA,YAAA,OAAO,MAAM3E,YAAY,CAACP,MAAM,CAAC;kBAAEiB,GAAG;kBAAEC,OAAO;kBAAE4B,KAAK;IAAEuB,cAAAA,MAAAA;IAAO,aAAC,CAAC,CAAA;eACpE,CACD,OAAOmB,QAAQ,EAAE;gBACb,IAAIA,QAAQ,YAAYC,KAAK,EAAE;IAC3BN,cAAAA,GAAG,GAAGK,QAAQ,CAAA;IAClB,aAAA;IACJ,WAAA;IACJ,SAAA;YACA,IAAI,IAAI,CAACH,aAAa,EAAE;IACpB,UAA2C;IACvC;IACA;gBACA5D,gBAAM,CAACoD,cAAc,CAAE,CAAkC,iCAAA,CAAA,GACpD,CAAGD,CAAAA,EAAAA,gCAAc,CAAC3D,GAAG,CAAE,CAAA,uCAAA,CAAwC,CAAC,CAAA;IACrEQ,YAAAA,gBAAM,CAAC8D,KAAK,CAAE,CAAiB,gBAAA,CAAA,EAAEjB,KAAK,CAAC,CAAA;IACvC7C,YAAAA,gBAAM,CAAC8D,KAAK,CAACJ,GAAG,CAAC,CAAA;gBACjB1D,gBAAM,CAACyD,QAAQ,EAAE,CAAA;IACrB,WAAA;IACA,UAAA,OAAO,IAAI,CAACG,aAAa,CAACrF,MAAM,CAAC;gBAAEiB,GAAG;gBAAEC,OAAO;IAAE4B,YAAAA,KAAAA;IAAM,WAAC,CAAC,CAAA;IAC7D,SAAA;IACA,QAAA,MAAMqC,GAAG,CAAA;IACb,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,OAAOpC,eAAe,CAAA;IAC1B,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIwB,EAAAA,iBAAiBA,CAAC;QAAEtD,GAAG;QAAEmD,UAAU;QAAElD,OAAO;IAAE4B,IAAAA,KAAAA;IAAO,GAAC,EAAE;IACpD,IAAA,MAAMH,MAAM,GAAG,IAAI,CAACH,OAAO,CAACmC,GAAG,CAACzD,OAAO,CAACd,MAAM,CAAC,IAAI,EAAE,CAAA;IACrD,IAAA,KAAK,MAAMkE,KAAK,IAAI3B,MAAM,EAAE;IACxB,MAAA,IAAI0B,MAAM,CAAA;IACV;IACA;IACA,MAAA,MAAMqB,WAAW,GAAGpB,KAAK,CAACnE,KAAK,CAAC;YAAEc,GAAG;YAAEmD,UAAU;YAAElD,OAAO;IAAE4B,QAAAA,KAAAA;IAAM,OAAC,CAAC,CAAA;IACpE,MAAA,IAAI4C,WAAW,EAAE;IACb,QAA2C;IACvC;IACA;cACA,IAAIA,WAAW,YAAYlC,OAAO,EAAE;IAChC/B,YAAAA,gBAAM,CAACkE,IAAI,CAAE,CAAA,cAAA,EAAgBf,gCAAc,CAAC3D,GAAG,CAAE,CAAA,WAAA,CAAY,GACxD,CAAqD,oDAAA,CAAA,GACrD,CAA6D,4DAAA,CAAA,EAAEqD,KAAK,CAAC,CAAA;IAC9E,WAAA;IACJ,SAAA;IACA;IACA;IACAD,QAAAA,MAAM,GAAGqB,WAAW,CAAA;IACpB,QAAA,IAAIV,KAAK,CAACC,OAAO,CAACZ,MAAM,CAAC,IAAIA,MAAM,CAACuB,MAAM,KAAK,CAAC,EAAE;IAC9C;IACAvB,UAAAA,MAAM,GAAGwB,SAAS,CAAA;IACtB,SAAC,MACI,IAAIH,WAAW,CAACxF,WAAW,KAAK4F,MAAM;IAAI;YAC3CA,MAAM,CAACC,IAAI,CAACL,WAAW,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE;IACvC;IACAvB,UAAAA,MAAM,GAAGwB,SAAS,CAAA;IACtB,SAAC,MACI,IAAI,OAAOH,WAAW,KAAK,SAAS,EAAE;IACvC;IACA;IACA;IACArB,UAAAA,MAAM,GAAGwB,SAAS,CAAA;IACtB,SAAA;IACA;YACA,OAAO;cAAEvB,KAAK;IAAED,UAAAA,MAAAA;aAAQ,CAAA;IAC5B,OAAA;IACJ,KAAA;IACA;IACA,IAAA,OAAO,EAAE,CAAA;IACb,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI2B,EAAAA,iBAAiBA,CAACxG,OAAO,EAAEY,MAAM,GAAGf,aAAa,EAAE;QAC/C,IAAI,CAACqD,kBAAkB,CAACuD,GAAG,CAAC7F,MAAM,EAAEb,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAA;IAClE,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACIc,eAAeA,CAACd,OAAO,EAAE;IACrB,IAAA,IAAI,CAAC6F,aAAa,GAAG9F,gBAAgB,CAACC,OAAO,CAAC,CAAA;IAClD,GAAA;IACA;IACJ;IACA;IACA;IACA;MACI0G,aAAaA,CAAC5B,KAAK,EAAE;IACjB,IAA2C;IACvC7E,MAAAA,gBAAM,CAACM,MAAM,CAACuE,KAAK,EAAE,QAAQ,EAAE;IAC3B3E,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBC,QAAAA,SAAS,EAAE,OAAA;IACf,OAAC,CAAC,CAAA;IACFL,MAAAA,gBAAM,CAACC,SAAS,CAAC4E,KAAK,EAAE,OAAO,EAAE;IAC7B3E,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBC,QAAAA,SAAS,EAAE,OAAA;IACf,OAAC,CAAC,CAAA;UACFL,gBAAM,CAACM,MAAM,CAACuE,KAAK,CAAC9E,OAAO,EAAE,QAAQ,EAAE;IACnCG,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBC,QAAAA,SAAS,EAAE,OAAA;IACf,OAAC,CAAC,CAAA;UACFL,gBAAM,CAACC,SAAS,CAAC4E,KAAK,CAAC9E,OAAO,EAAE,QAAQ,EAAE;IACtCG,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBC,QAAAA,SAAS,EAAE,eAAA;IACf,OAAC,CAAC,CAAA;UACFL,gBAAM,CAACM,MAAM,CAACuE,KAAK,CAAClE,MAAM,EAAE,QAAQ,EAAE;IAClCT,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBC,QAAAA,SAAS,EAAE,cAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,IAAI,CAAC,IAAI,CAAC0C,OAAO,CAACkC,GAAG,CAACJ,KAAK,CAAClE,MAAM,CAAC,EAAE;UACjC,IAAI,CAACoC,OAAO,CAACyD,GAAG,CAAC3B,KAAK,CAAClE,MAAM,EAAE,EAAE,CAAC,CAAA;IACtC,KAAA;IACA;IACA;IACA,IAAA,IAAI,CAACoC,OAAO,CAACmC,GAAG,CAACL,KAAK,CAAClE,MAAM,CAAC,CAACqE,IAAI,CAACH,KAAK,CAAC,CAAA;IAC9C,GAAA;IACA;IACJ;IACA;IACA;IACA;MACI6B,eAAeA,CAAC7B,KAAK,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC9B,OAAO,CAACkC,GAAG,CAACJ,KAAK,CAAClE,MAAM,CAAC,EAAE;IACjC,MAAA,MAAM,IAAIgG,4BAAY,CAAC,4CAA4C,EAAE;YACjEhG,MAAM,EAAEkE,KAAK,CAAClE,MAAAA;IAClB,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,MAAMiG,UAAU,GAAG,IAAI,CAAC7D,OAAO,CAACmC,GAAG,CAACL,KAAK,CAAClE,MAAM,CAAC,CAACkG,OAAO,CAAChC,KAAK,CAAC,CAAA;IAChE,IAAA,IAAI+B,UAAU,GAAG,CAAC,CAAC,EAAE;IACjB,MAAA,IAAI,CAAC7D,OAAO,CAACmC,GAAG,CAACL,KAAK,CAAClE,MAAM,CAAC,CAACmG,MAAM,CAACF,UAAU,EAAE,CAAC,CAAC,CAAA;IACxD,KAAC,MACI;IACD,MAAA,MAAM,IAAID,4BAAY,CAAC,uCAAuC,CAAC,CAAA;IACnE,KAAA;IACJ,GAAA;IACJ;;ICvYA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA,IAAII,aAAa,CAAA;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACO,MAAMC,wBAAwB,GAAGA,MAAM;MAC1C,IAAI,CAACD,aAAa,EAAE;IAChBA,IAAAA,aAAa,GAAG,IAAIjE,MAAM,EAAE,CAAA;IAC5B;QACAiE,aAAa,CAAC5D,gBAAgB,EAAE,CAAA;QAChC4D,aAAa,CAACtD,gBAAgB,EAAE,CAAA;IACpC,GAAA;IACA,EAAA,OAAOsD,aAAa,CAAA;IACxB,CAAC;;ICzBD;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASN,aAAaA,CAACQ,OAAO,EAAElH,OAAO,EAAEY,MAAM,EAAE;IAC7C,EAAA,IAAIkE,KAAK,CAAA;IACT,EAAA,IAAI,OAAOoC,OAAO,KAAK,QAAQ,EAAE;QAC7B,MAAMC,UAAU,GAAG,IAAI1C,GAAG,CAACyC,OAAO,EAAEtE,QAAQ,CAACF,IAAI,CAAC,CAAA;IAClD,IAA2C;IACvC,MAAA,IAAI,EAAEwE,OAAO,CAACvC,UAAU,CAAC,GAAG,CAAC,IAAIuC,OAAO,CAACvC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;IAC1D,QAAA,MAAM,IAAIiC,4BAAY,CAAC,gBAAgB,EAAE;IACrCzG,UAAAA,UAAU,EAAE,iBAAiB;IAC7BE,UAAAA,QAAQ,EAAE,eAAe;IACzBC,UAAAA,SAAS,EAAE,SAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACA;IACA;IACA,MAAA,MAAM8G,YAAY,GAAGF,OAAO,CAACvC,UAAU,CAAC,MAAM,CAAC,GACzCwC,UAAU,CAACtF,QAAQ,GACnBqF,OAAO,CAAA;IACb;UACA,MAAMG,SAAS,GAAG,QAAQ,CAAA;IAC1B,MAAA,IAAI,IAAIjG,MAAM,CAAE,CAAA,EAAEiG,SAAU,CAAA,CAAC,CAAC,CAAC5E,IAAI,CAAC2E,YAAY,CAAC,EAAE;YAC/CnF,gBAAM,CAACI,KAAK,CAAE,CAA6D,4DAAA,CAAA,GACtE,cAAagF,SAAU,CAAA,yCAAA,CAA0C,GACjE,CAAA,4DAAA,CAA6D,CAAC,CAAA;IACvE,OAAA;IACJ,KAAA;QACA,MAAMC,aAAa,GAAGA,CAAC;IAAE7F,MAAAA,GAAAA;IAAI,KAAC,KAAK;IAC/B,MAA2C;IACvC,QAAA,IAAIA,GAAG,CAACI,QAAQ,KAAKsF,UAAU,CAACtF,QAAQ,IACpCJ,GAAG,CAACkB,MAAM,KAAKwE,UAAU,CAACxE,MAAM,EAAE;IAClCV,UAAAA,gBAAM,CAACI,KAAK,CAAE,CAAE6E,EAAAA,OAAQ,+CAA8C,GACjE,CAAA,EAAEzF,GAAG,CAACU,QAAQ,EAAG,CAAqD,oDAAA,CAAA,GACtE,+BAA8B,CAAC,CAAA;IACxC,SAAA;IACJ,OAAA;IACA,MAAA,OAAOV,GAAG,CAACiB,IAAI,KAAKyE,UAAU,CAACzE,IAAI,CAAA;SACtC,CAAA;IACD;QACAoC,KAAK,GAAG,IAAIrE,KAAK,CAAC6G,aAAa,EAAEtH,OAAO,EAAEY,MAAM,CAAC,CAAA;IACrD,GAAC,MACI,IAAIsG,OAAO,YAAY9F,MAAM,EAAE;IAChC;QACA0D,KAAK,GAAG,IAAIxC,WAAW,CAAC4E,OAAO,EAAElH,OAAO,EAAEY,MAAM,CAAC,CAAA;IACrD,GAAC,MACI,IAAI,OAAOsG,OAAO,KAAK,UAAU,EAAE;IACpC;QACApC,KAAK,GAAG,IAAIrE,KAAK,CAACyG,OAAO,EAAElH,OAAO,EAAEY,MAAM,CAAC,CAAA;IAC/C,GAAC,MACI,IAAIsG,OAAO,YAAYzG,KAAK,EAAE;IAC/BqE,IAAAA,KAAK,GAAGoC,OAAO,CAAA;IACnB,GAAC,MACI;IACD,IAAA,MAAM,IAAIN,4BAAY,CAAC,wBAAwB,EAAE;IAC7CzG,MAAAA,UAAU,EAAE,iBAAiB;IAC7BE,MAAAA,QAAQ,EAAE,eAAe;IACzBC,MAAAA,SAAS,EAAE,SAAA;IACf,KAAC,CAAC,CAAA;IACN,GAAA;IACA,EAAA,MAAM0G,aAAa,GAAGC,wBAAwB,EAAE,CAAA;IAChDD,EAAAA,aAAa,CAACN,aAAa,CAAC5B,KAAK,CAAC,CAAA;IAClC,EAAA,OAAOA,KAAK,CAAA;IAChB;;IC3FA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAShE,eAAeA,CAACd,OAAO,EAAE;IAC9B,EAAA,MAAMgH,aAAa,GAAGC,wBAAwB,EAAE,CAAA;IAChDD,EAAAA,aAAa,CAAClG,eAAe,CAACd,OAAO,CAAC,CAAA;IAC1C;;ICrBA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASwG,iBAAiBA,CAACxG,OAAO,EAAE;IAChC,EAAA,MAAMgH,aAAa,GAAGC,wBAAwB,EAAE,CAAA;IAChDD,EAAAA,aAAa,CAACR,iBAAiB,CAACxG,OAAO,CAAC,CAAA;IAC5C;;;;;;;;;;;;;;;;"}