<?php
session_start();
include 'conn.php';

echo "<h2>Payvessel API Test</h2>";

// Check if user is logged in
if (!isset($_SESSION['data']['username'])) {
    echo "<p style='color: red;'>❌ User not logged in</p>";
    echo "<p>Please login first to test Payvessel API.</p>";
    exit();
}

$username = $_SESSION['data']['username'];
$data = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM users WHERE username = '$username'"));

echo "<h3>User Information:</h3>";
echo "<p><strong>Username:</strong> " . htmlspecialchars($username) . "</p>";
echo "<p><strong>Email:</strong> " . htmlspecialchars($data['email']) . "</p>";
echo "<p><strong>Name:</strong> " . htmlspecialchars($data['name']) . "</p>";
echo "<p><strong>Phone:</strong> " . htmlspecialchars($data['phone']) . "</p>";

echo "<h3>KYC Status:</h3>";
// Use uppercase column names to match database
if (!empty($data['BVN']) && !empty($data['NIN'])) {
    echo "<p style='color: green;'>✅ KYC Completed</p>";
    echo "<p><strong>BVN:</strong> " . substr($data['BVN'], 0, 4) . "****" . substr($data['BVN'], -4) . "</p>";
    echo "<p><strong>NIN:</strong> " . substr($data['NIN'], 0, 4) . "****" . substr($data['NIN'], -4) . "</p>";
} elseif (!empty($data['BVN']) && empty($data['NIN'])) {
    echo "<p style='color: orange;'>⚠️ Partial KYC - BVN provided, NIN missing</p>";
    echo "<p><strong>BVN:</strong> " . substr($data['BVN'], 0, 4) . "****" . substr($data['BVN'], -4) . "</p>";
    echo "<p><strong>NIN:</strong> Not provided</p>";
    echo "<p>You need to add NIN to complete KYC.</p>";
    exit();
} elseif (empty($data['BVN']) && !empty($data['NIN'])) {
    echo "<p style='color: orange;'>⚠️ Partial KYC - NIN provided, BVN missing</p>";
    echo "<p><strong>BVN:</strong> Not provided</p>";
    echo "<p><strong>NIN:</strong> " . substr($data['NIN'], 0, 4) . "****" . substr($data['NIN'], -4) . "</p>";
    echo "<p>You need to add BVN to complete KYC.</p>";
    exit();
} else {
    echo "<p style='color: red;'>❌ KYC Not Completed</p>";
    echo "<p>BVN: " . (empty($data['BVN']) ? 'Not provided' : 'Provided') . "</p>";
    echo "<p>NIN: " . (empty($data['NIN']) ? 'Not provided' : 'Provided') . "</p>";
    echo "<p>You need to complete KYC verification first.</p>";
    exit();
}

// Get Payvessel configuration
$config = mysqli_fetch_assoc(mysqli_query($con, "SELECT * FROM config"));
$payvesselConfig = json_decode($config['payvessel'], true);

if (!$payvesselConfig || !isset($payvesselConfig[0]) || !isset($payvesselConfig[1])) {
    echo "<p style='color: red;'>❌ Payvessel configuration not found</p>";
    exit();
}

$apiKey = $payvesselConfig[0];
$apiSecret = $payvesselConfig[1];

echo "<h3>Payvessel Configuration:</h3>";
echo "<p><strong>API Key:</strong> " . substr($apiKey, 0, 10) . "..." . "</p>";
echo "<p><strong>API Secret:</strong> " . substr($apiSecret, 0, 10) . "..." . "</p>";

// Test with correct format based on Payvessel documentation
$endpoint = "https://api.payvessel.com/pms/api/external/request/customerReservedAccount/";

echo "<h3>Testing with Customer Name as Account Name:</h3>";

// Test 1: Try with account_name parameter
$requestData1 = [
    "email" => $data['email'],
    "name" => $data['name'],
    "phoneNumber" => $data['phone'],
    "bankcode" => ["999991", "120001"], // PalmPay and 9Payment Service Bank
    "account_type" => "STATIC",
    "businessid" => "252C8036327246D3BA4D6B50D408B23C", // Actual Business ID
    "bvn" => $data['BVN'],
    "nin" => $data['NIN'],
    "account_name" => $data['name'] // Try to set custom account name
];

echo "<h4>Test 1: With account_name parameter</h4>";
echo "<p><strong>Request Data:</strong></p>";
echo "<pre>" . json_encode($requestData1, JSON_PRETTY_PRINT) . "</pre>";

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $endpoint,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => json_encode($requestData1),
    CURLOPT_HTTPHEADER => [
        "api-key: " . $apiKey,
        "api-secret: Bearer " . $apiSecret,
        "Content-Type: application/json"
    ],
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$err = curl_error($curl);
curl_close($curl);

echo "<p><strong>Response (HTTP $httpCode):</strong></p>";
if ($err) {
    echo "<p style='color: red;'>❌ cURL Error: $err</p>";
} else {
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "<p><strong>Parsed Response:</strong></p>";
        echo "<pre>" . json_encode($responseData, JSON_PRETTY_PRINT) . "</pre>";
        
        if ($httpCode === 200 && isset($responseData['status']) && $responseData['status'] === true) {
            echo "<p style='color: green;'>✅ SUCCESS! Virtual account created!</p>";
            
            if (isset($responseData['banks']) && is_array($responseData['banks'])) {
                echo "<h4>Generated Accounts:</h4>";
                foreach ($responseData['banks'] as $account) {
                    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
                    echo "<p><strong>Bank:</strong> " . htmlspecialchars($account['bankName']) . "</p>";
                    echo "<p><strong>Account Number:</strong> " . htmlspecialchars($account['accountNumber']) . "</p>";
                    echo "<p><strong>Account Name:</strong> " . htmlspecialchars($account['accountName']) . "</p>";
                    echo "<p><strong>Type:</strong> " . htmlspecialchars($account['account_type']) . "</p>";
                    if (isset($account['expire_date'])) {
                        echo "<p><strong>Expires:</strong> " . htmlspecialchars($account['expire_date']) . "</p>";
                    }
                    echo "</div>";
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠️ API call failed</p>";
            if (isset($responseData['message'])) {
                echo "<p><strong>Error:</strong> " . $responseData['message'] . "</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to parse JSON response</p>";
    }
}

echo "<hr>";

// Test 2: Try with customer_name parameter
echo "<h4>Test 2: With customer_name parameter</h4>";
$requestData2 = [
    "email" => $data['email'],
    "name" => $data['name'],
    "phoneNumber" => $data['phone'],
    "bankcode" => ["999991", "120001"],
    "account_type" => "STATIC",
    "businessid" => "252C8036327246D3BA4D6B50D408B23C",
    "bvn" => $data['BVN'],
    "nin" => $data['NIN'],
    "customer_name" => $data['name'] // Try customer_name parameter
];

echo "<p><strong>Request Data:</strong></p>";
echo "<pre>" . json_encode($requestData2, JSON_PRETTY_PRINT) . "</pre>";

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $endpoint,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => json_encode($requestData2),
    CURLOPT_HTTPHEADER => [
        "api-key: " . $apiKey,
        "api-secret: Bearer " . $apiSecret,
        "Content-Type: application/json"
    ],
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$err = curl_error($curl);
curl_close($curl);

echo "<p><strong>Response (HTTP $httpCode):</strong></p>";
if ($err) {
    echo "<p style='color: red;'>❌ cURL Error: $err</p>";
} else {
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "<p><strong>Parsed Response:</strong></p>";
        echo "<pre>" . json_encode($responseData, JSON_PRETTY_PRINT) . "</pre>";
        
        if ($httpCode === 200 && isset($responseData['status']) && $responseData['status'] === true) {
            echo "<p style='color: green;'>✅ SUCCESS! Virtual account created!</p>";
            
            if (isset($responseData['banks']) && is_array($responseData['banks'])) {
                echo "<h4>Generated Accounts:</h4>";
                foreach ($responseData['banks'] as $account) {
                    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
                    echo "<p><strong>Bank:</strong> " . htmlspecialchars($account['bankName']) . "</p>";
                    echo "<p><strong>Account Number:</strong> " . htmlspecialchars($account['accountNumber']) . "</p>";
                    echo "<p><strong>Account Name:</strong> " . htmlspecialchars($account['accountName']) . "</p>";
                    echo "<p><strong>Type:</strong> " . htmlspecialchars($account['account_type']) . "</p>";
                    if (isset($account['expire_date'])) {
                        echo "<p><strong>Expires:</strong> " . htmlspecialchars($account['expire_date']) . "</p>";
                    }
                    echo "</div>";
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠️ API call failed</p>";
            if (isset($responseData['message'])) {
                echo "<p><strong>Error:</strong> " . $responseData['message'] . "</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to parse JSON response</p>";
    }
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<p><a href='web/app-/payvessel_virtual_account.php'>View Virtual Account</a></p>";
echo "<p><a href='web/app-/payvessel_kyc.php'>Complete KYC</a></p>";

mysqli_close($con);
?> 