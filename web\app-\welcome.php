<?php include('lazy-includes/header.php');
$username = $data['username'];
$email = $data['email'];
$all_user_types = [];
$uQ = mysqli_query($con, "SELECT * FROM user_types");
while($uT = mysqli_fetch_assoc($uQ)){
  array_push($all_user_types, $uT);
}

$trs = mysqli_num_rows(mysqli_query($con, "SELECT id FROM transactions WHERE username = '$username' OR email = '$email'"));

// Helper function for balance visibility toggle (optional)
// function toggleBalanceVisibility() {
//     const balanceElement = document.getElementById('balanceAmount');
//     const toggleIcon = document.getElementById('toggleBalanceIcon');
//     if (balanceElement.type === 'password') {
//         balanceElement.type = 'text';
//         toggleIcon.classList.remove('bi-eye-slash-fill');
//         toggleIcon.classList.add('bi-eye-fill');
//     } else {
//         balanceElement.type = 'password';
//         toggleIcon.classList.remove('bi-eye-fill');
//         toggleIcon.classList.add('bi-eye-slash-fill');
//     }
// }

?>

  <?php if ($data['status'] == 'verified' && isset($_SESSION['config']['pop_info']) && !empty(trim($_SESSION['config']['pop_info']))): ?>
        <script>
					$(function(){
						var pop_up_notification = '<?=$_SESSION['config']['pop_info']?>';
						if(pop_up_notification != ""){
						Swal.fire("Notification", pop_up_notification, 'info');
						}
					});
			</script>
<?php endif ?>

        <!-- main page content -->
        <div class="main-container container mt-4">
        	
        <!-- Greeting section -->
        <div class="row mb-3">
            <div class="col">
                <h4 class="text-primary"><span id="greeting"></span>, <?= explode(' ', $data['name'])[0] ?>!</h4>
            </div>
        </div>
        
    <!-- Balance Card -->
    <div class="card text-white bg-primary mb-4 shadow">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-12 col-sm-8">
                    <p class="text-white-50 small mb-1">Available Balance</p>
                    <h4 class="fw-bold mb-2 mb-sm-0">
                        <span id="balanceAmount" data-balance="₦<?=parseAmt($data['bal'])?>">*****</span> <!-- Balance initially hidden -->
                        <i id="toggleBalanceIcon" class="bi bi-eye-slash-fill ms-2 align-middle" style="cursor: pointer;" onclick="toggleBalanceVisibility()"></i> <!-- Eye icon -->
                    </h4>
                </div>
                <div class="col-12 col-sm-4 d-flex flex-column align-items-start align-items-sm-end">
                    <a href="transactions.php" class="text-white small mb-2 d-block">Transaction History</a>
                    <a href="card.php" class="btn btn-light btn-sm w-100 w-sm-auto"><i class="bi bi-plus-circle"></i> Add Money</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Referral Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="card-title mb-2">REFER AND EARN</h6>
                    <!--<p class="text-muted mb-2">Did you know you can make up to ₦5000 daily if you refer people to use this platform? Try it today!</p>-->
                    <div class="d-flex flex-column flex-sm-row align-items-stretch align-items-sm-center gap-2">
                        <input type="text" id="referralLink" class="form-control form-control-sm flex-grow-1" value="http://<?= $config['site_link'] ?>/web/sign_up?referrer=<?= $data['username'] ?>" readonly>
                        <button onclick="copyReferralLink()" class="btn btn-primary btn-sm">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row text-center g-2">
                <div class="col-6 col-sm-3">
                    <a href="data.php" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-wifi fs-4 text-primary"></i>
                        </div>
                        <p class="small mb-0">Data</p>
                    </a>
                </div>
                <div class="col-6 col-sm-3">
                    <a href="airtime.php" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-phone fs-4 text-success"></i>
                        </div>
                        <p class="small mb-0">Airtime</p>
                    </a>
                </div>
                <div class="col-6 col-sm-3">
                    <a href="electricity.php" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-lightning-charge-fill fs-4 text-warning"></i>
                        </div>
                        <p class="small mb-0">Bills</p>
                    </a>
                </div>
                <div class="col-6 col-sm-3">
                    <a href="#" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-gem fs-4 text-warning"></i>
                        </div>
                        <p class="small mb-0">Mining</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
                                                    
                                                    
    <!-- More Services (Previously Menu) -->
    <div class="row mb-2">
        <div class="col">
            <h6 class="title">More Services</h6>
                                                        </div>
                                                    </div>
                                                    
    <div class="card shadow-sm mb-4">
                                                            <div class="card-body">
             <div class="row text-center g-3">
                <!-- Row 1 -->
                <div class="col-4">
                    <a href="cabletv.php" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-tv fs-4 text-primary"></i>
                        </div>
                        <p class="small mb-0">Cable TV</p>
                    </a>
                </div>
                <div class="col-4">
                    <a href="a2c.php" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-credit-card-2-front fs-4 text-info"></i>
                        </div>
                        <p class="small mb-0">Airtime To Cash</p>
                    </a>
                </div>
                <div class="col-4">
                    <a href="transactions.php" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-receipt fs-4 text-secondary"></i>
                        </div>
                        <p class="small mb-0">Transactions</p>
                    </a>
                </div>

                <!-- Row 2 -->
                <div class="col-4">
                    <a href="monnify_kyc.php" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-bank fs-4 text-primary"></i>
                        </div>
                        <p class="small mb-0">Account Reservation</p>
                    </a>
                </div>
                <div class="col-4">
                    <a href="data-card.php" class="text-decoration-none text-dark d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-sd-card fs-4 text-success"></i>
                        </div>
                        <p class="small mb-0">Data Card</p>
                    </a>
                </div>
                <div class="col-4">
                    <a href="#" class="text-decoration-none text-secondary d-block p-2">
                        <div class="avatar avatar-60 shadow-sm rounded-circle bg-light mb-2 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-grid-fill fs-4 text-muted"></i>
                        </div>
                        <p class="small mb-0">More</p>
                    </a>
                </div>
                
                        </div>
                    </div>
                </div>    
                
                


    <!-- Add JavaScript for Balance Visibility Toggle -->
    <script>
        function toggleBalanceVisibility() {
            const balanceElement = document.getElementById('balanceAmount');
            const toggleIcon = document.getElementById('toggleBalanceIcon');
            const actualBalance = balanceElement.getAttribute('data-balance');

            if (balanceElement.textContent === '*****') {
                // Show balance
                balanceElement.textContent = actualBalance;
                toggleIcon.classList.remove('bi-eye-slash-fill');
                toggleIcon.classList.add('bi-eye-fill');
            } else {
                // Hide balance
                balanceElement.textContent = '*****';
                toggleIcon.classList.remove('bi-eye-fill');
                toggleIcon.classList.add('bi-eye-slash-fill');
            }
        }

        function copyReferralLink() {
            const referralLink = document.getElementById('referralLink');
            referralLink.select();
            document.execCommand('copy');
            
            // Show feedback
            const button = event.currentTarget;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i> Copied!';
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        }
    </script>

</div> <!-- main-container -->

<!-- WhatsApp Chat Widget -->
<?php
// Get primary WhatsApp number for widget
$whatsapp_numbers = [];
if (isset($config['whatsapp_num']) && !empty($config['whatsapp_num'])) {
    // Try decoding as JSON array first
    $decoded_whatsapp = json_decode($config['whatsapp_num'], true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_whatsapp)) {
        $whatsapp_numbers = $decoded_whatsapp;
    } elseif (is_string($config['whatsapp_num'])) {
         // Fallback: treat as a single number if not valid JSON
        $whatsapp_numbers = [$config['whatsapp_num']];
    }
}

// Get primary WhatsApp number for widget
$primary_whatsapp = !empty($whatsapp_numbers) ? $whatsapp_numbers[0] : '';
// Clean the number (remove non-digits)
$primary_whatsapp_clean = preg_replace('/[^0-9]/', '', $primary_whatsapp);
// Ensure it starts with country code, assuming 234 if it starts with 0 and is local length
if (substr($primary_whatsapp_clean, 0, 1) === '0' && (strlen($primary_whatsapp_clean) == 11 || strlen($primary_whatsapp_clean) == 10)) {
    $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 1);
} elseif (substr($primary_whatsapp_clean, 0, 3) === '234' && strlen($primary_whatsapp_clean) > 13) {
    // Handle cases like +2340... by removing the 0
    $primary_whatsapp_clean = '234' . substr($primary_whatsapp_clean, 3);
} elseif (substr($primary_whatsapp_clean, 0, 1) === '+') {
    $primary_whatsapp_clean = substr($primary_whatsapp_clean, 1); // Remove leading + if present
}
?>

<?php if (!empty($primary_whatsapp_clean)): ?>
<div class="whatsapp-widget">
    <!-- Floating chat button -->
    <button class="whatsapp-button" id="openWhatsappChat">
        <i class="bi bi-whatsapp"></i>
    </button>

    <!-- Chat popup -->
    <div class="whatsapp-popup" id="whatsappChatPopup">
        <div class="popup-header">
            <div class="popup-header-content">
                <img src="assets/img/logo.png" alt="<?= htmlspecialchars($config['site_name']) ?>" width="40">
                <div>
                    <h6 class="mb-0"><?= htmlspecialchars($config['site_name']) ?> Support</h6>
                    <small class="text-muted">Usually replies within an hour</small>
                </div>
            </div>
            <button class="close-btn" id="closeWhatsappChat">×</button>
        </div>
        <div class="popup-body">
            <div class="message-container">
                <div class="received-message">
                    <p>Hello! How can we help you today?</p>
                    <small class="message-time">Now</small>
                </div>
            </div>
            <div class="message-input">
                <form id="whatsappMessageForm" class="d-flex">
                    <input type="text" id="whatsappMessage" class="form-control" placeholder="Type a message..." required>
                    <button type="submit" class="btn btn-success ms-2">
                        <i class="bi bi-arrow-right-circle-fill"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Responsive Improvements & WhatsApp Widget Styles -->
<style>
    /* Mobile Responsive Improvements */
    @media (max-width: 576px) {
        .main-container {
            padding-left: 10px !important;
            padding-right: 10px !important;
        }

        .card-body {
            padding: 1rem 0.75rem !important;
        }

        .avatar {
            width: 50px !important;
            height: 50px !important;
        }

        .avatar i {
            font-size: 1.2rem !important;
        }

        .small {
            font-size: 0.75rem !important;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.75rem !important;
        }

        .form-control-sm {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.8rem !important;
        }

        .whatsapp-popup {
            width: 280px !important;
            right: -10px !important;
        }

        .whatsapp-widget {
            bottom: 15px !important;
            right: 15px !important;
        }

        .whatsapp-button {
            width: 50px !important;
            height: 50px !important;
            font-size: 24px !important;
        }
    }

    @media (max-width: 480px) {
        .main-container {
            margin-top: 0.5rem !important;
        }

        .mb-4 {
            margin-bottom: 1rem !important;
        }

        .mb-3 {
            margin-bottom: 0.75rem !important;
        }

        .avatar {
            width: 45px !important;
            height: 45px !important;
        }

        .col-4 {
            margin-bottom: 1rem;
        }

        .whatsapp-popup {
            width: 260px !important;
            right: -20px !important;
        }
    }

    /* Improved touch targets for mobile */
    .avatar {
        transition: transform 0.2s ease;
    }

    .avatar:hover {
        transform: scale(1.05);
    }

    /* Better spacing for mobile */
    .g-3 {
        --bs-gutter-x: 0.75rem;
        --bs-gutter-y: 0.75rem;
    }

    /* Improved button responsiveness */
    .btn {
        min-height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Avatar improvements */
    .avatar {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Improved card spacing */
    .card {
        border-radius: 12px;
        border: none;
    }

    /* Better text readability on mobile */
    @media (max-width: 576px) {
        .text-primary {
            font-size: 1.1rem !important;
        }

        .card-title {
            font-size: 1rem !important;
        }

        .title {
            font-size: 1rem !important;
            font-weight: 600 !important;
        }
    }
    .whatsapp-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }

    .whatsapp-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #25D366;
        color: white;
        border: none;
        font-size: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        transition: all 0.3s;
    }

    .whatsapp-button:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 14px rgba(0, 0, 0, 0.2);
    }

    .whatsapp-popup {
        position: absolute;
        bottom: 80px;
        right: 0;
        width: 320px;
        background-color: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
        display: none;
    }

    .popup-header {
        background-color: #25D366;
        color: white;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .popup-header-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .popup-header img {
        border-radius: 50%;
        background-color: white;
        padding: 5px;
    }

    .close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
    }

    .popup-body {
        display: flex;
        flex-direction: column;
        height: 300px;
    }

    .message-container {
        flex-grow: 1;
        padding: 15px;
        overflow-y: auto;
    }

    .received-message,
    .sent-message {
        max-width: 80%;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 10px;
    }

    .received-message {
        background-color: #f0f0f0;
        align-self: flex-start;
    }

    .sent-message {
        background-color: #dcf8c6;
        align-self: flex-end;
        margin-left: auto;
    }

    .message-time {
        display: block;
        font-size: 10px;
        margin-top: 5px;
        opacity: 0.6;
    }

    .message-input {
        border-top: 1px solid #e0e0e0;
        padding: 10px;
    }

    .message-input form {
        display: flex;
        gap: 8px;
    }

    .message-input input {
        flex-grow: 1;
        border: 1px solid #e0e0e0;
        border-radius: 20px;
        padding: 8px 15px;
        outline: none;
    }

    .message-input button {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #25D366;
        border: none;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
    }

    .message-input button:hover {
        background-color: #128C7E;
        transform: scale(1.05);
    }
</style>

<!-- WhatsApp Widget Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const openBtn = document.getElementById('openWhatsappChat');
        const closeBtn = document.getElementById('closeWhatsappChat');
        const popup = document.getElementById('whatsappChatPopup');
        const messageForm = document.getElementById('whatsappMessageForm');
        const messageInput = document.getElementById('whatsappMessage');
        const messageContainer = document.querySelector('.message-container');
        
        // WhatsApp number from database
        const whatsappNumber = '<?= $primary_whatsapp_clean ?>';
        
        // Toggle chat popup
        openBtn.addEventListener('click', function() {
            popup.style.display = 'block';
        });
        
        closeBtn.addEventListener('click', function() {
            popup.style.display = 'none';
        });
        
        // Handle message sending
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (message) {
                // Add sent message to chat
                const sentMsg = document.createElement('div');
                sentMsg.className = 'sent-message';
                sentMsg.innerHTML = `
                    <p>${message}</p>
                    <small class="message-time">Just now</small>
                `;
                messageContainer.appendChild(sentMsg);
                
                // Clear input
                messageInput.value = '';
                
                // Scroll to bottom
                messageContainer.scrollTop = messageContainer.scrollHeight;
                
                // Open WhatsApp in new tab after a brief delay
                setTimeout(() => {
                    const encodedMessage = encodeURIComponent(message);
                    window.open(`https://wa.me/${whatsappNumber}?text=${encodedMessage}`, '_blank');
                    
                    // Hide popup after sending
                    popup.style.display = 'none';
                }, 500);
            }
        });
    });
</script>
<?php endif; ?>

<?php include('lazy-includes/footer2.php');?>